#!/usr/bin/env python3
"""
测试幻觉检测功能的Python脚本
分析TBD目录中的SRT文件，验证我们的检测算法是否正确
"""

import os
import re
from collections import Counter

def parse_srt_time(time_str):
    """解析SRT时间戳为秒数"""
    # 格式: 00:13:36,940
    parts = time_str.split(':')
    if len(parts) != 3:
        return 0.0
    
    hours = float(parts[0])
    minutes = float(parts[1])
    seconds_parts = parts[2].split(',')
    seconds = float(seconds_parts[0])
    milliseconds = float(seconds_parts[1]) / 1000.0 if len(seconds_parts) > 1 else 0.0
    
    return hours * 3600.0 + minutes * 60.0 + seconds + milliseconds

def analyze_srt_file(file_path):
    """分析SRT文件中的重复内容"""
    print(f"\n📋 分析文件: {os.path.basename(file_path)}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.strip().split('\n')
    
    repetition_count = Counter()
    short_intervals = 0
    total_intervals = 0
    consecutive_same = 0
    max_consecutive = 0
    last_text = ""
    
    i = 0
    while i < len(lines):
        # 跳过序号行
        if lines[i].strip().isdigit():
            i += 1
            if i >= len(lines):
                break
            
            # 解析时间戳行
            if '-->' in lines[i]:
                time_parts = lines[i].split('-->')
                if len(time_parts) == 2:
                    start_time = parse_srt_time(time_parts[0].strip())
                    end_time = parse_srt_time(time_parts[1].strip())
                    duration = end_time - start_time
                    
                    total_intervals += 1
                    if duration < 1.5:  # 小于1.5秒的间隔认为异常短
                        short_intervals += 1
                
                i += 1
                
                # 解析文本内容
                text_lines = []
                while i < len(lines) and lines[i].strip():
                    text_lines.append(lines[i].strip())
                    i += 1
                
                text = ' '.join(text_lines).strip()
                if text:
                    # 统计重复文本
                    repetition_count[text] += 1
                    
                    # 检测连续相同文本
                    if text == last_text:
                        consecutive_same += 1
                        max_consecutive = max(max_consecutive, consecutive_same)
                    else:
                        consecutive_same = 0
                    last_text = text
        i += 1
    
    # 分析结果
    print(f"  总时间段数: {total_intervals}")
    print(f"  异常短间隔数: {short_intervals}")
    if total_intervals > 0:
        short_ratio = short_intervals / total_intervals
        print(f"  异常短间隔比例: {short_ratio:.1%}")
    
    print(f"  最大连续重复次数: {max_consecutive}")
    
    # 找出高频重复的短文本
    high_freq_repeats = []
    for text, count in repetition_count.items():
        if count >= 5 and len(text) <= 10:
            high_freq_repeats.append((text, count))
    
    if high_freq_repeats:
        print(f"  高频重复短文本:")
        for text, count in sorted(high_freq_repeats, key=lambda x: x[1], reverse=True):
            print(f"    \"{text}\": {count}次")
    
    # 判断是否有幻觉问题
    has_hallucination = False
    issues = []
    
    if max_consecutive >= 3:
        has_hallucination = True
        issues.append(f"连续相同文本{max_consecutive}次")
    
    if high_freq_repeats:
        has_hallucination = True
        issues.append(f"高频重复短文本{len(high_freq_repeats)}个")
    
    if total_intervals > 0 and short_intervals / total_intervals > 0.3:
        has_hallucination = True
        issues.append(f"异常短间隔{short_ratio:.1%}")
    
    if has_hallucination:
        print(f"  🚨 检测到幻觉问题: {', '.join(issues)}")
    else:
        print(f"  ✅ 未检测到幻觉问题")
    
    return has_hallucination, issues

def main():
    """主函数"""
    print("🧠 幻觉检测功能测试")
    print("=" * 50)
    
    tbd_dir = "TBD/script"
    if not os.path.exists(tbd_dir):
        print(f"❌ 目录不存在: {tbd_dir}")
        return
    
    srt_files = [f for f in os.listdir(tbd_dir) if f.endswith('.srt')]
    
    if not srt_files:
        print(f"❌ 在 {tbd_dir} 中没有找到SRT文件")
        return
    
    print(f"📁 找到 {len(srt_files)} 个SRT文件")
    
    total_files = 0
    hallucination_files = 0
    
    for srt_file in srt_files:
        file_path = os.path.join(tbd_dir, srt_file)
        has_hallucination, issues = analyze_srt_file(file_path)
        
        total_files += 1
        if has_hallucination:
            hallucination_files += 1
    
    print("\n" + "=" * 50)
    print(f"📊 检测结果汇总:")
    print(f"  总文件数: {total_files}")
    print(f"  有幻觉问题: {hallucination_files}")
    print(f"  正常文件: {total_files - hallucination_files}")
    print(f"  幻觉检出率: {hallucination_files/total_files:.1%}")
    
    if hallucination_files > 0:
        print(f"\n🎯 结论: 检测到幻觉问题，智能重试系统应该被触发")
    else:
        print(f"\n✅ 结论: 未检测到幻觉问题，文件质量良好")

if __name__ == "__main__":
    main()
