#!/bin/bash

# Whisper 批量测试脚本 - 专门用于测试不同的抗幻觉策略
# 作者: Whisper Batch Processor
# 用途: 测试不同参数组合对"鸡尾酒会效应"问题的解决效果

# 配置变量
MODEL_PATH="/Users/<USER>/Documents/whisper_models/ggml-large-v3.bin"
TEST_FILE="/Users/<USER>/Desktop/杨宁老师随缘开示/audio/01/以佛心来布施，功德无量.mp3"
OUTPUT_DIR="/Users/<USER>/Desktop/whisper_test_results"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo "🎯 Whisper 抗幻觉测试开始"
echo "测试文件: $TEST_FILE"
echo "输出目录: $OUTPUT_DIR"
echo "========================================"

# 测试1: 基础诊断
echo "📊 测试1: 基础诊断 (了解问题严重程度)"
whisper-cli -m "$MODEL_PATH" \
  -l zh \
  -otxt -osrt \
  -of "$OUTPUT_DIR/01_baseline" \
  "$TEST_FILE"

echo "✅ 基础测试完成"
echo ""

# 测试2: 保守抗幻觉策略
echo "🛡️ 测试2: 保守抗幻觉策略"
whisper-cli -m "$MODEL_PATH" \
  -l zh \
  -bo 2 -bs 2 \
  --temperature 0.0,0.2,0.4,0.6,0.8,1.0 \
  --logprob-thold -0.5 \
  --no-speech-thold 0.8 \
  --entropy-thold 3.0 \
  -otxt -osrt \
  -of "$OUTPUT_DIR/02_conservative" \
  "$TEST_FILE"

echo "✅ 保守策略测试完成"
echo ""

# 测试3: 激进抗幻觉策略
echo "⚡ 测试3: 激进抗幻觉策略"
whisper-cli -m "$MODEL_PATH" \
  -l zh \
  -bo 1 -bs 1 \
  --temperature 0.2,0.4,0.6,0.8,1.0 \
  --logprob-thold -0.3 \
  --no-speech-thold 0.9 \
  --entropy-thold 2.0 \
  --word-thold 0.02 \
  --max-len 50 \
  -otxt -osrt \
  -of "$OUTPUT_DIR/03_aggressive" \
  "$TEST_FILE"

echo "✅ 激进策略测试完成"
echo ""

# 测试4: 鸡尾酒会专用策略
echo "🍸 测试4: 鸡尾酒会专用策略"
whisper-cli -m "$MODEL_PATH" \
  -l zh \
  -bo 3 -bs 3 \
  --temperature 0.0,0.1,0.3,0.5,0.7,1.0 \
  --logprob-thold -0.7 \
  --no-speech-thold 0.7 \
  --entropy-thold 3.5 \
  --word-thold 0.005 \
  -otxt -osrt \
  -of "$OUTPUT_DIR/04_cocktail_party" \
  "$TEST_FILE"

echo "✅ 鸡尾酒会策略测试完成"
echo ""

# 测试5: 超保守策略 (最后手段)
echo "🔒 测试5: 超保守策略 (最后手段)"
whisper-cli -m "$MODEL_PATH" \
  -l zh \
  -bo 1 -bs 1 \
  --temperature 0.0 \
  --logprob-thold -0.1 \
  --no-speech-thold 0.95 \
  --entropy-thold 1.5 \
  --word-thold 0.05 \
  --max-len 30 \
  -otxt -osrt \
  -of "$OUTPUT_DIR/05_ultra_conservative" \
  "$TEST_FILE"

echo "✅ 超保守策略测试完成"
echo ""

# 生成测试报告
echo "📋 生成测试报告..."
REPORT_FILE="$OUTPUT_DIR/test_report.md"

cat > "$REPORT_FILE" << EOF
# Whisper 抗幻觉测试报告

测试时间: $(date)
测试文件: $TEST_FILE
模型: large-v3

## 测试策略说明

### 1. 基础诊断
- 目的: 了解问题的严重程度和发生位置
- 参数: 默认参数
- 文件: 01_baseline.txt, 01_baseline.srt

### 2. 保守抗幻觉策略
- 目的: 温和地提高识别稳定性
- 关键参数: 
  - beam-size: 2 (适中的搜索宽度)
  - temperature: 0.0-1.0 (完整温度回退)
  - logprob-thold: -0.5 (中等置信度要求)
  - no-speech-thold: 0.8 (较高的静音检测阈值)
- 文件: 02_conservative.txt, 02_conservative.srt

### 3. 激进抗幻觉策略
- 目的: 强制打破重复循环
- 关键参数:
  - beam-size: 1 (最窄搜索，强制决策)
  - temperature: 0.2-1.0 (跳过最低温度)
  - logprob-thold: -0.3 (高置信度要求)
  - max-len: 50 (限制输出长度)
- 文件: 03_aggressive.txt, 03_aggressive.srt

### 4. 鸡尾酒会专用策略
- 目的: 专门处理多人声重叠场景
- 关键参数:
  - beam-size: 3 (平衡搜索宽度)
  - 更宽松的阈值设置
  - 更精细的温度控制
- 文件: 04_cocktail_party.txt, 04_cocktail_party.srt

### 5. 超保守策略
- 目的: 最后手段，牺牲部分内容换取稳定性
- 关键参数:
  - temperature: 0.0 (无随机性)
  - 极高的置信度要求
  - 极短的输出长度限制
- 文件: 05_ultra_conservative.txt, 05_ultra_conservative.srt

## 使用说明

1. 检查每个输出文件，找出哪种策略效果最好
2. 特别关注重复性幻觉开始的时间点
3. 比较不同策略在该时间点的处理效果
4. 选择最佳策略应用到批量处理工具中

## 参数解释

- **beam-size**: 搜索宽度，越小越保守
- **temperature**: 随机性，0.0最确定，1.0最随机
- **logprob-thold**: 对数概率阈值，越高要求置信度越高
- **no-speech-thold**: 静音检测阈值，越高越容易判定为静音
- **entropy-thold**: 熵阈值，越低要求输出越确定
- **word-thold**: 单词置信度阈值
- **max-len**: 最大输出长度限制

EOF

echo "📄 测试报告已生成: $REPORT_FILE"
echo ""
echo "🎉 所有测试完成！"
echo "请检查输出文件并选择最佳策略。"
