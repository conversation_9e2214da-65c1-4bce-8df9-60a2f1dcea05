#!/bin/bash

# 测试保守重试顺序的脚本
# 验证新的重试顺序：Level4 → Level3 → Level2 → Level1

echo "🛡️ 测试保守重试顺序"
echo "新的重试策略：优先使用最保守、成功率最高的方法"
echo "重试顺序：Level4(分段处理) → Level3(极保守) → Level2(严格阈值) → Level1(温度回退)"
echo "========================================"

# 创建测试目录
TEST_DIR="/Users/<USER>/Desktop/conservative_retry_test"
mkdir -p "$TEST_DIR"

# 复制一个已知有幻觉问题的文件
echo "📁 准备测试文件..."
cp "TBD/我们一定要走出来，不然这场有关情感的痛苦轮回，无穷无尽…….mp3" "$TEST_DIR/test_hallucination.mp3"

echo "🎯 测试目标:"
echo "  1. 验证初次处理会检测到幻觉问题"
echo "  2. 验证重试顺序为 Level4 → Level3 → Level2 → Level1"
echo "  3. 验证最保守的方法能够成功处理"
echo "  4. 验证生成的报告包含正确的重试信息"
echo ""

echo "📋 预期行为:"
echo "  - 初次处理: 检测到重复内容，触发重试"
echo "  - 第1次重试: Level4-分段处理模式"
echo "  - 如果Level4失败: Level3-极保守模式"
echo "  - 如果Level3失败: Level2-严格阈值模式"
echo "  - 如果Level2失败: Level1-温度回退模式"
echo "  - 最终结果: 重试成功，使用最保守的有效方法"
echo ""

echo "🚀 启动测试..."
echo "请在批量处理工具中："
echo "  1. 选择测试目录: $TEST_DIR"
echo "  2. 选择SRT输出格式（必须，用于幻觉检测）"
echo "  3. 选择任意质量模式（建议选择性能优化模式）"
echo "  4. 观察重试过程的日志输出"
echo ""

# 创建预期结果说明
cat > "$TEST_DIR/EXPECTED_RESULTS.md" << EOF
# 🛡️ 保守重试顺序测试 - 预期结果

## 📋 测试文件
- **文件名**: test_hallucination.mp3
- **已知问题**: 连续重复600+次相同文本
- **预期**: 初次处理必定失败，触发智能重试

## 🔄 预期重试流程

### 1️⃣ 初次处理
```
  - 步骤 4: 正在检查重复内容...
    -> ⚠️ 检测到重复内容: 连续相同短句 - 检测到XXX次连续相同文本
⚠️ 初次处理失败，尝试自动重试...
```

### 2️⃣ 智能重试系统启动
```
🧠 尝试Level4-分段处理重试: /path/to/test_hallucination.mp3
  - 步骤 4: 正在检查重复内容...
    -> ✅ 未检测到重复内容
🔄 重试成功: test_hallucination.mp3 (使用Level4-分段处理模式)
```

**或者如果Level4失败**:
```
🧠 尝试Level4-分段处理重试: /path/to/test_hallucination.mp3
⚠️ Level4-分段处理重试仍有幻觉问题: 连续相同短句 - XXX
🧠 尝试Level3-极保守重试: /path/to/test_hallucination.mp3
  - 步骤 4: 正在检查重复内容...
    -> ✅ 未检测到重复内容
🔄 重试成功: test_hallucination.mp3 (使用Level3-极保守模式)
```

## 📊 预期统计报告

```
📊 智能重试统计报告
--------------------------------------------------
总文件数: 1
初次成功: 0 个
重试成功: 1 个
  - Level4成功: 1 个  ← 最理想情况
  或
  - Level3成功: 1 个  ← 次理想情况
  或
  - Level2成功: 1 个  ← 可接受情况
  或
  - Level1成功: 1 个  ← 最后手段
完全失败: 0 个
--------------------------------------------------

重试成功详情:
1. test_hallucination.mp3 - Level4成功 (连续相同短句问题)
```

## ✅ 成功标准

1. **重试顺序正确**: 必须按 Level4 → Level3 → Level2 → Level1 顺序
2. **检测准确**: 能正确识别重复内容问题
3. **重试成功**: 至少有一个级别能成功处理
4. **报告完整**: 生成详细的重试过程报告
5. **文件质量**: 最终生成的SRT文件无重复内容

## 🎯 关键观察点

- **重试级别**: 观察实际使用的重试级别
- **处理时间**: Level4应该比Level1处理时间更长（更保守）
- **文件质量**: 检查最终SRT文件是否真的解决了重复问题
- **日志完整性**: 确认每个重试步骤都有清晰的日志输出

EOF

echo "📄 预期结果说明已保存至: $TEST_DIR/EXPECTED_RESULTS.md"
echo ""
echo "🎯 现在请运行批量处理工具进行测试！"
echo "测试目录: $TEST_DIR"
