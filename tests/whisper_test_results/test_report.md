# Whisper 抗幻觉测试报告

测试时间: Wed Aug 13 22:52:51 CST 2025
测试文件: /Users/<USER>/Desktop/杨宁老师随缘开示/audio/01/以佛心来布施，功德无量.mp3
模型: large-v3

## 测试策略说明

### 1. 基础诊断
- 目的: 了解问题的严重程度和发生位置
- 参数: 默认参数
- 文件: 01_baseline.txt, 01_baseline.srt

### 2. 保守抗幻觉策略
- 目的: 温和地提高识别稳定性
- 关键参数: 
  - beam-size: 2 (适中的搜索宽度)
  - temperature: 0.0-1.0 (完整温度回退)
  - logprob-thold: -0.5 (中等置信度要求)
  - no-speech-thold: 0.8 (较高的静音检测阈值)
- 文件: 02_conservative.txt, 02_conservative.srt

### 3. 激进抗幻觉策略
- 目的: 强制打破重复循环
- 关键参数:
  - beam-size: 1 (最窄搜索，强制决策)
  - temperature: 0.2-1.0 (跳过最低温度)
  - logprob-thold: -0.3 (高置信度要求)
  - max-len: 50 (限制输出长度)
- 文件: 03_aggressive.txt, 03_aggressive.srt

### 4. 鸡尾酒会专用策略
- 目的: 专门处理多人声重叠场景
- 关键参数:
  - beam-size: 3 (平衡搜索宽度)
  - 更宽松的阈值设置
  - 更精细的温度控制
- 文件: 04_cocktail_party.txt, 04_cocktail_party.srt

### 5. 超保守策略
- 目的: 最后手段，牺牲部分内容换取稳定性
- 关键参数:
  - temperature: 0.0 (无随机性)
  - 极高的置信度要求
  - 极短的输出长度限制
- 文件: 05_ultra_conservative.txt, 05_ultra_conservative.srt

## 使用说明

1. 检查每个输出文件，找出哪种策略效果最好
2. 特别关注重复性幻觉开始的时间点
3. 比较不同策略在该时间点的处理效果
4. 选择最佳策略应用到批量处理工具中

## 参数解释

- **beam-size**: 搜索宽度，越小越保守
- **temperature**: 随机性，0.0最确定，1.0最随机
- **logprob-thold**: 对数概率阈值，越高要求置信度越高
- **no-speech-thold**: 静音检测阈值，越高越容易判定为静音
- **entropy-thold**: 熵阈值，越低要求输出越确定
- **word-thold**: 单词置信度阈值
- **max-len**: 最大输出长度限制

