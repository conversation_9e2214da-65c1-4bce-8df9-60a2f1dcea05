#!/bin/bash

# 测试单个文件的智能重试系统
# 使用TBD目录中已知有幻觉问题的文件

echo "🧠 测试智能重试系统"
echo "使用已知有幻觉问题的文件进行测试"
echo "========================================"

# 创建测试目录
TEST_DIR="/Users/<USER>/Desktop/intelligent_retry_single_test"
mkdir -p "$TEST_DIR"

# 复制一个有问题的文件到测试目录
cp "TBD/我们一定要走出来，不然这场有关情感的痛苦轮回，无穷无尽…….mp3" "$TEST_DIR/"

echo "📁 测试文件已复制到: $TEST_DIR"
echo "🎯 预期行为:"
echo "  1. 初次处理应该检测到幻觉问题并失败"
echo "  2. 自动触发智能重试系统"
echo "  3. 使用Level1-4参数逐级重试"
echo "  4. 生成详细的重试报告"
echo ""

# 运行批量处理工具
echo "🚀 启动批量处理工具..."
echo "请选择SRT输出格式以便进行幻觉检测"
echo ""

cd "$TEST_DIR"
/Users/<USER>/Desktop/Projects/Finished/clone-test/temp/whisper_batch_processor/target/release/whisper_batch_processor
