#!/bin/bash

# 平衡模式 vs 高质量模式对比测试脚本
# 目的：测试添加温度回退机制后，平衡模式和高质量模式的差距

# 配置变量
MODEL_PATH="/Users/<USER>/Documents/whisper_models/ggml-large-v3.bin"
TEST_FILE="/Users/<USER>/Desktop/杨宁老师随缘开示/audio/01/以佛心来布施，功德无量.mp3"
OUTPUT_DIR="/Users/<USER>/Desktop/balanced_vs_highquality_test"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo "🎯 平衡模式 vs 高质量模式对比测试"
echo "测试文件: $TEST_FILE"
echo "输出目录: $OUTPUT_DIR"
echo "========================================"

# 测试1: 当前平衡模式 (默认参数 + 温度回退)
echo "⚖️  测试1: 平衡模式 (默认参数 + 温度回退)"
echo "参数: --temperature 0.0,0.2,0.4,0.6,0.8,1.0"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  --temperature 0.0,0.2,0.4,0.6,0.8,1.0 \
  -otxt -osrt \
  -of "$OUTPUT_DIR/balanced_mode" \
  "$TEST_FILE"

echo "✅ 平衡模式测试完成"
echo ""

# 测试2: 更新后的高质量模式 (beam-size=5 + 温度回退)
echo "💎 测试2: 高质量模式 (beam-size=5 + 温度回退)"
echo "参数: -bo 5 -bs 5 --temperature 0.0,0.2,0.4,0.6,0.8,1.0"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  -bo 5 -bs 5 \
  --temperature 0.0,0.2,0.4,0.6,0.8,1.0 \
  -otxt -osrt \
  -of "$OUTPUT_DIR/highquality_mode" \
  "$TEST_FILE"

echo "✅ 高质量模式测试完成"
echo ""

# 测试3: 原始高质量模式 (仅beam-size=5，无温度回退)
echo "🔧 测试3: 原始高质量模式 (仅beam-size=5，无温度回退)"
echo "参数: -bo 5 -bs 5"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  -bo 5 -bs 5 \
  -otxt -osrt \
  -of "$OUTPUT_DIR/original_highquality" \
  "$TEST_FILE"

echo "✅ 原始高质量模式测试完成"
echo ""

# 测试4: 纯默认参数 (01_baseline复现)
echo "📊 测试4: 纯默认参数 (01_baseline复现)"
echo "参数: 无额外参数"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  -otxt -osrt \
  -of "$OUTPUT_DIR/pure_default" \
  "$TEST_FILE"

echo "✅ 纯默认参数测试完成"
echo ""

# 生成对比分析报告
echo "📋 生成对比分析报告..."
REPORT_FILE="$OUTPUT_DIR/comparison_report.md"

cat > "$REPORT_FILE" << EOF
# 平衡模式 vs 高质量模式对比测试报告

测试时间: $(date)
测试文件: $TEST_FILE
模型: large-v3

## 测试配置对比

| 模式 | 参数配置 | 说明 |
|------|----------|------|
| 平衡模式 | \`--temperature 0.0,0.2,0.4,0.6,0.8,1.0\` | 默认beam-size + 温度回退 |
| 高质量模式 | \`-bo 5 -bs 5 --temperature 0.0,0.2,0.4,0.6,0.8,1.0\` | 高beam-size + 温度回退 |
| 原始高质量 | \`-bo 5 -bs 5\` | 仅高beam-size，无温度回退 |
| 纯默认参数 | 无额外参数 | 01_baseline复现 |

## 理论分析

### 平衡模式特点
- **优势**: 使用whisper-cli优化的默认beam-size，处理速度快
- **温度回退**: 提供稳定性，防止重复性幻觉
- **适用**: 日常批量处理，速度与质量平衡

### 高质量模式特点  
- **优势**: 更大的搜索空间(beam-size=5)，理论上质量更高
- **温度回退**: 同样的稳定性保障
- **代价**: 处理时间更长，计算资源消耗更大
- **适用**: 重要音频，质量要求极高的场景

### 预期差异
1. **质量**: 高质量模式应该在准确性上略胜一筹
2. **速度**: 平衡模式应该明显更快
3. **稳定性**: 两者都有温度回退，稳定性应该相当
4. **内容完整性**: 需要通过实际测试验证

## 评估维度

请检查以下文件并进行对比：

### 文本文件
- \`balanced_mode.txt\` - 平衡模式结果
- \`highquality_mode.txt\` - 高质量模式结果  
- \`original_highquality.txt\` - 原始高质量模式结果
- \`pure_default.txt\` - 纯默认参数结果

### 字幕文件
- \`balanced_mode.srt\` - 平衡模式字幕
- \`highquality_mode.srt\` - 高质量模式字幕
- \`original_highquality.srt\` - 原始高质量模式字幕
- \`pure_default.srt\` - 纯默认参数字幕

## 对比检查清单

### 1. 基础统计
- [ ] 文件行数对比
- [ ] 字符数对比
- [ ] 处理时间对比

### 2. 质量评估
- [ ] 专业术语识别准确性
- [ ] 语言流畅性
- [ ] 时间戳准确性
- [ ] 是否有重复性幻觉

### 3. 关键时间点检查
- [ ] 7:30-8:30 时间段对比
- [ ] 复杂音频片段处理效果
- [ ] 多人声重叠部分的处理

### 4. 实用性评估
- [ ] 哪种模式更适合日常使用？
- [ ] 质量提升是否值得时间成本？
- [ ] 是否需要调整默认推荐？

## 使用建议

基于测试结果，请考虑：

1. **如果高质量模式明显更好**: 考虑将其设为新的推荐默认
2. **如果差异很小**: 保持平衡模式作为默认，高质量模式用于特殊场景
3. **如果原始高质量模式表现不佳**: 证实了温度回退的重要性
4. **如果纯默认参数最好**: 考虑进一步简化平衡模式

## 下一步行动

1. 运行统计分析: \`wc -l *.txt && wc -c *.txt\`
2. 关键词检查: \`grep -c "法华经\|布施" *.txt\`
3. 质量人工评估: 随机抽查几个时间段的转录质量
4. 根据结果调整批量工具的默认设置

EOF

echo "📄 对比分析报告已生成: $REPORT_FILE"
echo ""

# 快速统计分析
echo "📊 快速统计分析:"
echo "文件行数统计:"
wc -l "$OUTPUT_DIR"/*.txt

echo ""
echo "文件字符数统计:"
wc -c "$OUTPUT_DIR"/*.txt

echo ""
echo "关键词统计:"
echo "法华经出现次数:"
grep -c "法华经" "$OUTPUT_DIR"/*.txt

echo ""
echo "布施出现次数:"
grep -c "布施" "$OUTPUT_DIR"/*.txt

echo ""
echo "🎉 对比测试完成！"
echo "请查看输出文件并阅读分析报告: $REPORT_FILE"
echo "建议重点关注质量差异和处理时间的权衡。"
