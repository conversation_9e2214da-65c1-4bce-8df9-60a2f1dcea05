#!/bin/bash

# 智能重试机制测试脚本
# 测试新的重复内容检测和分层重试功能

# 配置变量
MODEL_PATH="/Users/<USER>/Documents/whisper_models/ggml-large-v3.bin"
TEST_FILE="/Users/<USER>/Desktop/杨宁老师随缘开示/audio/01/以佛心来布施，功德无量.mp3"
OUTPUT_DIR="/Users/<USER>/Desktop/intelligent_retry_test"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo "🧠 智能重试机制测试"
echo "测试文件: $TEST_FILE"
echo "输出目录: $OUTPUT_DIR"
echo "========================================"

# 测试1: 故意使用容易产生幻觉的参数
echo "🔍 测试1: 使用容易产生幻觉的参数"
echo "参数: 高温度，低阈值 (容易产生重复)"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  --temperature 1.0 \
  --logprob-thold 0.0 \
  --no-speech-thold 0.1 \
  -osrt \
  -of "$OUTPUT_DIR/hallucination_prone" \
  "$TEST_FILE"

echo "✅ 幻觉倾向测试完成"
echo ""

# 测试2: Level1 抗幻觉参数
echo "🛡️ 测试2: Level1 抗幻觉参数"
echo "参数: 温度回退 + 基础参数调整"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  --temperature 0.0,0.1,0.2 \
  --logprob-thold -0.8 \
  --no-speech-thold 0.9 \
  -osrt \
  -of "$OUTPUT_DIR/level1_anti_hallucination" \
  "$TEST_FILE"

echo "✅ Level1 测试完成"
echo ""

# 测试3: Level2 抗幻觉参数
echo "🛡️ 测试3: Level2 抗幻觉参数"
echo "参数: 更严格的阈值参数"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  --temperature 0.0,0.05,0.1 \
  --logprob-thold -1.0 \
  --no-speech-thold 0.95 \
  --entropy-thold 1.5 \
  --word-thold 0.005 \
  -osrt \
  -of "$OUTPUT_DIR/level2_anti_hallucination" \
  "$TEST_FILE"

echo "✅ Level2 测试完成"
echo ""

# 测试4: Level3 抗幻觉参数
echo "🛡️ 测试4: Level3 抗幻觉参数"
echo "参数: 极保守模式"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  --temperature 0.0 \
  --logprob-thold -1.2 \
  --no-speech-thold 0.98 \
  --entropy-thold 1.0 \
  --word-thold 0.001 \
  --max-len 30 \
  -osrt \
  -of "$OUTPUT_DIR/level3_anti_hallucination" \
  "$TEST_FILE"

echo "✅ Level3 测试完成"
echo ""

# 测试5: Level4 抗幻觉参数
echo "🛡️ 测试5: Level4 抗幻觉参数"
echo "参数: 分段处理模式"
time whisper-cli -m "$MODEL_PATH" \
  -l zh \
  --temperature 0.0 \
  --logprob-thold -1.5 \
  --no-speech-thold 0.99 \
  --entropy-thold 0.8 \
  --word-thold 0.0005 \
  --max-len 20 \
  -bo 1 -bs 1 \
  -osrt \
  -of "$OUTPUT_DIR/level4_anti_hallucination" \
  "$TEST_FILE"

echo "✅ Level4 测试完成"
echo ""

# 生成对比分析报告
echo "📋 生成对比分析报告..."
REPORT_FILE="$OUTPUT_DIR/intelligent_retry_analysis.md"

cat > "$REPORT_FILE" << EOF
# 🧠 智能重试机制测试分析报告

测试时间: $(date)
测试文件: $TEST_FILE
模型: large-v3

## 测试目的

验证新的智能重试机制：
1. 重复内容检测算法的准确性
2. 分层抗幻觉参数的有效性
3. 自动重试机制的智能程度

## 测试配置对比

| 测试 | 参数配置 | 目的 |
|------|----------|------|
| 幻觉倾向 | 高温度，低阈值 | 故意产生重复内容 |
| Level1 | 温度回退 + 基础调整 | 轻度抗幻觉 |
| Level2 | 严格阈值参数 | 中度抗幻觉 |
| Level3 | 极保守模式 | 重度抗幻觉 |
| Level4 | 分段处理模式 | 极限抗幻觉 |

## 重复内容检测指标

请检查以下SRT文件中的重复模式：

### 1. 连续相同短句检测
检查是否有连续3次以上的相同文本（如"嗯。"）

### 2. 高频重复文本检测
检查是否有短文本重复5次以上

### 3. 异常短时间间隔检测
检查是否有超过30%的时间间隔小于1.5秒

## 分析方法

\`\`\`bash
# 检查重复"嗯"的数量
grep -o "嗯" *.srt | wc -l

# 检查连续相同行
awk '/^[^0-9]/ {if(\$0==prev) count++; else count=1; if(count>max) max=count; prev=\$0} END {print "最大连续重复:", max}' *.srt

# 检查时间间隔
grep ">" *.srt | head -20
\`\`\`

## 预期结果

- **幻觉倾向测试**: 应该产生明显的重复内容
- **Level1**: 轻微改善，可能仍有少量重复
- **Level2**: 显著改善，重复大幅减少
- **Level3**: 几乎无重复，但可能过于保守
- **Level4**: 完全无重复，但可能丢失部分内容

## 成功标准

1. **检测准确性**: 能正确识别重复内容
2. **分层有效性**: 更高级别的参数产生更少重复
3. **内容完整性**: 在消除重复的同时保持内容完整

## 实际测试结果

请手动检查生成的SRT文件并填写：

### 幻觉倾向测试结果
- 重复"嗯"次数: ___
- 连续相同文本最大次数: ___
- 异常短间隔比例: ___%

### Level1 结果
- 重复"嗯"次数: ___
- 连续相同文本最大次数: ___
- 异常短间隔比例: ___%

### Level2 结果
- 重复"嗯"次数: ___
- 连续相同文本最大次数: ___
- 异常短间隔比例: ___%

### Level3 结果
- 重复"嗯"次数: ___
- 连续相同文本最大次数: ___
- 异常短间隔比例: ___%

### Level4 结果
- 重复"嗯"次数: ___
- 连续相同文本最大次数: ___
- 异常短间隔比例: ___%

## 结论

基于测试结果：
1. 最有效的抗幻觉级别是: ___
2. 推荐的默认重试顺序: ___
3. 需要调整的参数: ___

EOF

echo "📄 分析报告已生成: $REPORT_FILE"
echo ""

# 快速分析
echo "📊 快速分析结果:"
echo "各文件的'嗯'出现次数:"
for file in "$OUTPUT_DIR"/*.srt; do
    if [ -f "$file" ]; then
        count=$(grep -o "嗯" "$file" 2>/dev/null | wc -l)
        echo "$(basename "$file"): $count 次"
    fi
done

echo ""
echo "🎉 智能重试机制测试完成！"
echo "请查看生成的SRT文件并分析重复内容模式。"
echo "分析报告: $REPORT_FILE"
