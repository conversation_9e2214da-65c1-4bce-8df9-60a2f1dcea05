#!/bin/bash

echo "🎵 音频文件编码测试脚本"
echo "========================"

# 检查音频文件
echo "📁 检查音频文件..."
if [ ! -d "script/audio" ]; then
    echo "❌ script/audio 目录不存在"
    exit 1
fi

audio_count=$(find script/audio -name "*.mp3" | wc -l)
echo "找到 $audio_count 个音频文件"

if [ $audio_count -eq 0 ]; then
    echo "❌ 未找到音频文件"
    exit 1
fi

# 清理旧文件
echo "🧹 清理旧输出文件..."
rm -f script/*.srt script/*.txt script/*.vtt
rm -f script/reports/*.txt

echo "🚀 启动批量处理工具..."
echo "请选择 script/audio 作为输入目录"
echo "请选择 SRT 作为输出格式"
echo "请选择 性能优化模式 进行测试"
echo ""

# 运行处理工具
./target/release/whisper_batch_processor

echo ""
echo "📊 处理完成，检查结果..."

# 检查编码
echo "🔤 检查SRT文件编码:"
for srt in script/*.srt; do
    if [ -f "$srt" ]; then
        echo "$(basename "$srt"): $(file "$srt")"
    fi
done

echo ""
echo "📝 检查内容示例:"
for srt in script/*.srt; do
    if [ -f "$srt" ]; then
        echo "=== $(basename "$srt") ==="
        head -10 "$srt" | grep -v "^[0-9]*$" | grep -v "^$" | grep -v "-->" | head -3
        echo ""
    fi
done

echo "✅ 测试完成！"
