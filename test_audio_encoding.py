#!/usr/bin/env python3
"""
测试script/audio下的两个音频文件，验证所有模式的UTF-8编码输出
"""

import os
import subprocess
import time
import glob

def test_audio_files():
    print("🎵 测试script/audio下的音频文件编码输出")
    print("=" * 60)
    
    # 检查音频文件
    audio_files = glob.glob("script/audio/*.mp3")
    print(f"📁 找到 {len(audio_files)} 个音频文件:")
    for i, audio_file in enumerate(audio_files):
        print(f"  {i+1}. {os.path.basename(audio_file)}")
    
    if len(audio_files) == 0:
        print("❌ 未找到音频文件，请确保文件在script/audio/目录下")
        return
    
    print(f"\n🧹 清理旧的输出文件...")
    # 清理script目录下的旧文件，但保留audio子目录
    old_files = glob.glob("script/*.srt") + glob.glob("script/*.txt") + glob.glob("script/*.vtt")
    for old_file in old_files:
        try:
            os.remove(old_file)
            print(f"  删除: {os.path.basename(old_file)}")
        except:
            pass
    
    # 清理reports目录
    if os.path.exists("script/reports"):
        old_reports = glob.glob("script/reports/*.txt")
        for old_report in old_reports:
            try:
                os.remove(old_report)
                print(f"  删除: {old_report}")
            except:
                pass
    
    print(f"\n🚀 启动批量处理工具测试...")
    print("=" * 60)
    print("请按照以下步骤操作:")
    print("1. 选择 script/audio 作为输入目录")
    print("2. 选择 SRT 作为输出格式")
    print("3. 选择 性能优化模式 进行测试")
    print("4. 观察处理过程和重试情况")
    print("=" * 60)
    
    return True

def check_results_after_processing():
    """处理完成后检查结果"""
    print("\n📊 处理完成后的结果检查")
    print("=" * 60)
    
    # 检查生成的SRT文件
    srt_files = glob.glob("script/*.srt")
    print(f"📄 生成的SRT文件: {len(srt_files)} 个")
    
    for i, srt_file in enumerate(srt_files):
        print(f"\n📋 文件 {i+1}: {os.path.basename(srt_file)}")
        
        # 检查文件编码
        try:
            result = subprocess.run(['file', srt_file], 
                                  capture_output=True, text=True)
            encoding_info = result.stdout.strip()
            print(f"  🔤 编码检测: {encoding_info}")
            
            # 判断编码是否正确
            if "UTF-8" in encoding_info:
                print("  ✅ 编码正确: UTF-8")
            elif "Non-ISO extended-ASCII" in encoding_info:
                print("  ❌ 编码错误: 仍然是乱码编码")
            else:
                print(f"  ⚠️ 编码未知: {encoding_info}")
                
        except Exception as e:
            print(f"  ❌ 编码检测失败: {e}")
        
        # 检查文件内容
        try:
            with open(srt_file, 'r', encoding='utf-8') as f:
                content = f.read(300)
                lines = content.split('\n')
                
                print("  📝 内容预览:")
                content_lines = []
                for line in lines[:10]:
                    if line.strip() and not line.strip().isdigit() and '-->' not in line:
                        content_lines.append(line.strip())
                        if len(content_lines) >= 3:
                            break
                
                for j, line in enumerate(content_lines):
                    print(f"    {j+1}: {line}")
                    
                # 检查是否有乱码字符
                has_garbled = any(char in content for char in ['è', 'ä', 'å', 'ç', 'ï', 'ü'])
                if has_garbled:
                    print("  ❌ 检测到疑似乱码字符")
                else:
                    print("  ✅ 内容看起来正常")
                    
        except UnicodeDecodeError:
            print("  ❌ UTF-8解码失败，文件编码有问题")
            # 尝试其他编码
            try:
                with open(srt_file, 'r', encoding='latin1') as f:
                    content = f.read(200)
                    print(f"  🔍 Latin1编码内容: {content[:100]}...")
            except:
                print("  ❌ 无法读取文件内容")
        except Exception as e:
            print(f"  ❌ 读取文件失败: {e}")
    
    # 检查处理报告
    reports = glob.glob("script/reports/*.txt")
    if reports:
        latest_report = max(reports, key=os.path.getctime)
        print(f"\n📊 处理报告: {os.path.basename(latest_report)}")
        
        try:
            with open(latest_report, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 提取关键统计信息
                lines = content.split('\n')
                for line in lines:
                    if '总文件数:' in line or '成功处理:' in line or '处理失败:' in line:
                        print(f"  📈 {line.strip()}")
                    elif '重试模式:' in line:
                        print(f"  🔄 {line.strip()}")
                        
        except Exception as e:
            print(f"  ❌ 读取报告失败: {e}")
    else:
        print("\n⚠️ 未找到处理报告")
    
    print("\n" + "=" * 60)
    print("🎯 验证要点:")
    print("✅ SRT文件编码应显示为 'UTF-8 text'")
    print("✅ 中文内容应正常显示，无乱码")
    print("✅ 应该有合理的重试分布")
    print("✅ 首次成功率应有所提升")

def create_test_script():
    """创建自动化测试脚本"""
    script_content = '''#!/bin/bash

echo "🎵 音频文件编码测试脚本"
echo "========================"

# 检查音频文件
echo "📁 检查音频文件..."
if [ ! -d "script/audio" ]; then
    echo "❌ script/audio 目录不存在"
    exit 1
fi

audio_count=$(find script/audio -name "*.mp3" | wc -l)
echo "找到 $audio_count 个音频文件"

if [ $audio_count -eq 0 ]; then
    echo "❌ 未找到音频文件"
    exit 1
fi

# 清理旧文件
echo "🧹 清理旧输出文件..."
rm -f script/*.srt script/*.txt script/*.vtt
rm -f script/reports/*.txt

echo "🚀 启动批量处理工具..."
echo "请选择 script/audio 作为输入目录"
echo "请选择 SRT 作为输出格式"
echo "请选择 性能优化模式 进行测试"
echo ""

# 运行处理工具
./target/release/whisper_batch_processor

echo ""
echo "📊 处理完成，检查结果..."

# 检查编码
echo "🔤 检查SRT文件编码:"
for srt in script/*.srt; do
    if [ -f "$srt" ]; then
        echo "$(basename "$srt"): $(file "$srt")"
    fi
done

echo ""
echo "📝 检查内容示例:"
for srt in script/*.srt; do
    if [ -f "$srt" ]; then
        echo "=== $(basename "$srt") ==="
        head -10 "$srt" | grep -v "^[0-9]*$" | grep -v "^$" | grep -v "-->" | head -3
        echo ""
    fi
done

echo "✅ 测试完成！"
'''
    
    with open('test_audio_encoding.sh', 'w') as f:
        f.write(script_content)
    
    os.chmod('test_audio_encoding.sh', 0o755)
    print(f"\n📝 已创建自动化测试脚本: test_audio_encoding.sh")

if __name__ == "__main__":
    if test_audio_files():
        print("\n" + "="*60)
        print("⏳ 请运行批量处理工具测试音频文件...")
        print("完成后运行: python3 test_audio_encoding.py --check")
        print("或直接运行: ./test_audio_encoding.sh")
        
        create_test_script()
        
        # 如果用户传入--check参数，则检查结果
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == '--check':
            check_results_after_processing()
