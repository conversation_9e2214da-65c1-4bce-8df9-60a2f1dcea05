# 🎙️ Whisper 批量音频转录处理工具

[![Rust](https://img.shields.io/badge/rust-1.70+-orange.svg)](https://www.rust-lang.org)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Platform](https://img.shields.io/badge/platform-macOS%20%7C%20Linux%20%7C%20Windows-lightgrey)](https://github.com/your-repo/whisper-batch-processor)

一个基于 Rust 开发的高效 Whisper 批量音频转录工具，具备智能重复内容检测和自动重试机制，专门解决 Whisper 模型的幻觉问题。

## ✨ 核心特性

### 🧠 智能幻觉检测系统
- **无需 LLM**：纯算法实现的重复内容检测
- **多维度检测**：连续重复、高频重复、异常时间间隔
- **实时检测**：处理完成后自动检测输出质量

### 🔄 分层自动重试机制
- **4级抗幻觉参数**：从最保守到最激进的渐进式重试
- **智能重试顺序**：优先使用成功率最高的保守方法
- **自动文件管理**：失败文件隔离，中间文件清理

### ⚡ 性能优化
- **硬件加速**：充分利用 M1/M2/M3 芯片的 Metal GPU
- **多线程处理**：支持 8 核心并行处理
- **Flash Attention**：显著减少显存占用和计算时间

### 📊 详细报告系统
- **处理统计**：成功率、重试详情、失败分析
- **智能路径管理**：报告文件自动保存到合理位置
- **失败文件管理**：符号链接和详细清单

## 🚀 快速开始

### 系统要求

- **操作系统**：macOS 10.15+, Linux, Windows 10+
- **Rust 版本**：1.70 或更高
- **依赖工具**：whisper-cli, ffmpeg
- **推荐硬件**：Apple Silicon (M1/M2/M3) 或支持 CUDA 的 GPU

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/your-repo/whisper-batch-processor.git
cd whisper-batch-processor
```

2. **编译项目**
```bash
cargo build --release
```

3. **安装依赖**
```bash
# macOS (使用 Homebrew)
brew install whisper-cpp ffmpeg

# Linux (Ubuntu/Debian)
sudo apt install ffmpeg
# 需要手动安装 whisper-cpp

# Windows
# 请参考 docs/安装指南.md
```

4. **配置模型路径**
```bash
# 下载 Whisper 模型文件
mkdir -p ~/Documents/whisper_models
# 将模型文件放置到该目录
```

### 基本使用

```bash
# 运行批量处理工具
./target/release/whisper_batch_processor

# 按照交互式界面提示：
# 1. 选择音频文件目录
# 2. 选择输出格式 (推荐 SRT)
# 3. 选择质量模式 (推荐 性能优化模式)
```

## 📋 质量模式说明

| 模式 | 特点 | 适用场景 | 处理速度 |
|------|------|----------|----------|
| **快速模式** | 速度优先 | 大批量预处理 | ⚡⚡⚡⚡⚡ |
| **平衡模式** | 质量与速度平衡 | 日常使用推荐 | ⚡⚡⚡⚡ |
| **高质量模式** | 质量优先 | 重要音频 | ⚡⚡⚡ |
| **抗幻觉模式** | 专门对抗重复问题 | 问题音频 | ⚡⚡ |
| **鸡尾酒会模式** | 多人声重叠处理 | 复杂音频环境 | ⚡⚡ |
| **性能优化模式** | 最大化硬件性能 | M1/M2/M3 芯片 | ⚡⚡⚡⚡⚡ |

## 🧠 智能重试系统

### 检测维度

1. **连续相同短句检测**
   - 检测连续 3 次以上的相同文本
   - 专门解决 "嗯。" 类型的重复问题

2. **高频重复文本检测**
   - 检测短文本重复 5 次以上
   - 识别异常的高频词汇

3. **异常短时间间隔检测**
   - 检测小于 1.5 秒的异常短间隔
   - 识别时间戳分布异常

### 重试级别

```
Level 4 (最保守) → Level 3 (极保守) → Level 2 (严格) → Level 1 (温和)
```

- **Level 4**：分段处理模式，成功率最高
- **Level 3**：极保守参数，适合严重幻觉问题
- **Level 2**：严格阈值，平衡质量与效率
- **Level 1**：温度回退，轻微问题处理

## 📊 使用示例

### 处理结果示例

```
📊 智能重试统计报告
--------------------------------------------------
总文件数: 102
初次成功: 85 个
重试成功: 15 个
  - Level4成功: 10 个
  - Level3成功: 3 个
  - Level2成功: 2 个
完全失败: 2 个
总耗时: 3,245.68 秒
平均每文件: 31.82 秒
成功率: 98.0%
--------------------------------------------------
```

### 文件组织结构

```
your_audio_folder/
├── audio_file1.mp3
├── audio_file2.wav
└── script/
    ├── reports/
    │   └── whisper_batch_report_20240814_165607.txt
    ├── failed/
    │   ├── failed_files_list.txt
    │   └── problem_file.mp3 -> ../problem_file.mp3
    ├── audio_file1.srt
    └── audio_file2.srt
```

## 📚 文档

- [📖 详细使用指南](docs/使用指南.md)
- [🔧 安装配置指南](docs/安装指南.md)
- [🧠 智能重试系统详解](docs/智能重试系统.md)
- [🔍 幻觉检测算法](docs/幻觉检测核心算法.md)
- [⚡ 性能优化指南](docs/性能优化指南.md)
- [🧪 测试说明](docs/测试说明.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [whisper.cpp](https://github.com/ggerganov/whisper.cpp) - 高性能 Whisper 推理引擎
- [OpenAI Whisper](https://github.com/openai/whisper) - 强大的语音识别模型
- Rust 社区的优秀 crates

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看 [文档](docs/) 寻找解决方案
2. 搜索现有的 [Issues](https://github.com/your-repo/whisper-batch-processor/issues)
3. 创建新的 Issue 描述您的问题

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！
