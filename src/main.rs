// ================================================================================================
// Whisper 批量音频转录处理工具 (Whisper Batch Audio Transcription Processor)
//
// 功能特性 (Features)：
// - 批量处理多种音频格式 (MP3, WAV, M4A, FLAC)
// - 智能幻觉检测和自动重试机制 (Intelligent hallucination detection & auto-retry)
// - 分层抗幻觉参数优化 (Layered anti-hallucination parameter optimization)
// - 性能优化 (支持 M1/M2/M3 芯片硬件加速)
// - 详细的处理报告和失败文件管理 (Comprehensive reporting & failed file management)
//
// 作者 (Author): Whisper Batch Processor Team
// 版本 (Version): 1.0.0
// 许可证 (License): MIT
// ================================================================================================

use std::fs;                           // 文件系统操作 (File system operations)
use std::path::{Path, PathBuf};        // 路径处理 (Path handling)
use std::process::Command;             // 外部命令执行 (External command execution)
use std::fmt;                          // 格式化输出 (Formatting output)
use std::time::{Duration, Instant};    // 时间测量和统计 (Time measurement & statistics)
use std::io::Write;                    // 输入输出操作 (I/O operations)
use walkdir::WalkDir;                  // 目录遍历 (Directory traversal)
use dialoguer::{theme::ColorfulTheme, Input, Select}; // 交互式用户界面 (Interactive UI)
use indicatif::{ProgressBar, ProgressStyle}; // 进度条显示 (Progress bar display)
use log::{info, error, warn};          // 日志宏 (Logging macros)
use std::collections::HashMap;         // 哈希表，用于重复内容统计 (HashMap for repetition statistics)
use chrono::Local;                     // 本地时间处理 (Local time handling)
use clap::{Parser, ValueEnum};         // 命令行参数解析 (Command line argument parsing)
use serde::{Deserialize, Serialize};   // 序列化支持 (Serialization support)
use dirs;                              // 系统目录获取 (System directory access)

/// 配置文件结构体 (Configuration File Structure)
#[derive(Debug, Clone, Deserialize, Serialize)]
struct Config {
    /// 默认模型文件路径 (Default model file path)
    model_path: Option<String>,
    /// 默认语言设置 (Default language setting)
    language: Option<String>,
    /// 默认输出格式 (Default output format)
    default_format: Option<String>,
    /// 默认质量预设 (Default quality preset)
    default_quality: Option<String>,
    /// 默认线程数 (Default thread count)
    threads: Option<u32>,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            model_path: None,
            language: Some("zh".to_string()),
            default_format: Some("srt".to_string()),
            default_quality: Some("balanced".to_string()),
            threads: None, // Will be calculated based on CPU cores
        }
    }
}

/// 命令行参数结构体 (Command Line Arguments Structure)
#[derive(Parser)]
#[command(name = "whisper_batch_processor")]
#[command(about = "Whisper 批量音频转录处理工具 - 智能重试机制")]
#[command(version = "2.0.0")]
struct Args {
    /// 输入音频文件或目录路径 (Input audio file or directory path)
    #[arg(short, long, help = "输入音频文件或目录的路径")]
    input: Option<String>,

    /// 输出目录路径 (Output directory path)
    #[arg(short, long, help = "输出目录路径，默认为输入目录")]
    output: Option<String>,

    /// 语言设置 (Language setting)
    #[arg(short, long, help = "语音识别语言 (zh/en/ja/ko/auto)")]
    language: Option<String>,

    /// 输出格式 (Output format)
    #[arg(short, long, value_enum, help = "输出文件格式")]
    format: Option<OutputFormat>,

    /// 质量预设 (Quality preset)
    #[arg(short, long, value_enum, help = "质量预设: fast, balanced, high-fidelity")]
    quality: Option<QualityPreset>,

    /// 模型文件路径 (Model file path)
    #[arg(long, help = "Whisper模型文件(.bin)的路径")]
    model: Option<String>,

    /// 线程数 (Thread count)
    #[arg(long, help = "处理线程数，默认为CPU核心数的一半")]
    threads: Option<u32>,

    /// 禁用重试机制 (Disable retry mechanism)
    #[arg(long, help = "完全禁用智能重试机制")]
    no_retry: bool,

    /// 静默模式 (Silent mode)
    #[arg(long, help = "静默模式，减少输出信息")]
    silent: bool,
}

/// 质量预设枚举 (Quality Preset Enum)
#[derive(Debug, Clone, ValueEnum)]
enum QualityPreset {
    /// 快速模式 - 最大速度，适合快速草稿 (Fast mode - maximum speed, suitable for quick drafts)
    Fast,
    /// 平衡模式 - 速度与质量平衡，推荐日常使用 (Balanced mode - speed and quality balance, recommended for daily use)
    Balanced,
    /// 高保真模式 - 最高质量，适合重要音频 (High-fidelity mode - highest quality, suitable for important audio)
    HighFidelity,
}

/// 输出格式枚举 (Output Format Enum)
#[derive(Debug, Clone, ValueEnum, PartialEq)]
enum OutputFormat {
    /// TXT格式 (TXT format)
    Txt,
    /// SRT字幕格式 (SRT subtitle format)
    Srt,
    /// VTT字幕格式 (VTT subtitle format)
    Vtt,
}

/// 重复内容检测结果 (Hallucination Detection Result)
///
/// 用于存储智能幻觉检测算法的分析结果，包含检测状态、类型、置信度等信息
/// Used to store the analysis results of intelligent hallucination detection algorithm
#[derive(Debug, Clone)]
struct HallucinationDetection {
    /// 是否检测到重复内容 (Whether repetitive content is detected)
    has_repetition: bool,

    /// 重复类型描述 (Description of repetition type)
    /// 可能的值: "连续相同短句", "高频重复短句", "异常短时间间隔"
    repetition_type: String,

    /// 检测置信度 (Detection confidence level, 0.0-1.0)
    /// 值越高表示检测结果越可信
    confidence: f64,

    /// 详细信息 (Detailed information)
    /// 包含具体的重复次数、时间间隔等详细描述
    details: String,
}





/// 处理配置选项
#[derive(Debug, Clone)]
struct ProcessConfig {
    /// 支持的音频文件扩展名
    audio_extensions: Vec<String>,
    /// 音频采样率
    sample_rate: u32,
    /// 音频声道数
    channels: u32,
    /// 语音识别语言
    language: String,
    /// 输出格式
    output_format: OutputFormat,
    /// 模型文件路径
    model_path: PathBuf,
    /// 处理质量/速度设置
    quality_setting: QualitySetting,
}

/// 质量/速度设置 - 简化为3个清晰的预设 (Quality/Speed Settings - Simplified to 3 clear presets)
#[derive(Debug, Clone)]
enum QualitySetting {
    /// 快速模式 - 最大速度，适合快速草稿 (Fast mode - maximum speed, suitable for quick drafts)
    Fast,
    /// 平衡模式 - 速度与质量平衡，推荐日常使用 (Balanced mode - speed and quality balance, recommended for daily use)
    Balanced,
    /// 高保真模式 - 最高质量，适合重要音频 (High-fidelity mode - highest quality, suitable for important audio)
    HighQuality,
}

impl QualitySetting {
    fn as_str(&self) -> &str {
        match self {
            QualitySetting::Fast => "快速",
            QualitySetting::Balanced => "平衡",
            QualitySetting::HighQuality => "高保真",
        }
    }

    /// 获取whisper.cpp参数 (Get whisper.cpp parameters)
    /// 基于prompt要求的3个清晰预设 (Based on the 3 clear presets required by the prompt)
    fn whisper_args(&self) -> Vec<&str> {
        match self {
            // 快速模式：最大速度，适合快速草稿 (Fast mode: maximum speed, suitable for quick drafts)
            QualitySetting::Fast => vec!["-bo", "1", "-bs", "1"],

            // 平衡模式：速度与质量平衡，推荐日常使用 (Balanced mode: speed and quality balance, recommended for daily use)
            QualitySetting::Balanced => vec![
                "--temperature", "0.0,0.2,0.4,0.6,0.8,1.0"
            ],

            // 高保真模式：最高质量，适合重要音频 (High-fidelity mode: highest quality, suitable for important audio)
            QualitySetting::HighQuality => vec![
                "-bo", "5", "-bs", "5",
                "--temperature", "0.0,0.2,0.4,0.6,0.8,1.0",
                "--logprob-thold", "-0.8",
                "--no-speech-thold", "0.5"
            ],
        }
    }
}



impl Default for ProcessConfig {
    fn default() -> Self {
        Self {
            audio_extensions: vec!["mp3".to_string(), "wav".to_string(), "m4a".to_string(), "flac".to_string()],
            sample_rate: 16000,
            channels: 1,
            language: "zh".to_string(),
            output_format: OutputFormat::Txt,
            model_path: PathBuf::from("/Users/<USER>/Documents/whisper_models/ggml-large-v3.bin"),
            quality_setting: QualitySetting::Balanced, // 基于测试结果，平衡模式提供最佳质量
        }
    }
}

impl OutputFormat {
    fn as_str(&self) -> &str {
        match self {
            OutputFormat::Txt => "txt",
            OutputFormat::Srt => "srt",
            OutputFormat::Vtt => "vtt",
        }
    }

    fn whisper_arg(&self) -> &str {
        match self {
            OutputFormat::Txt => "-otxt",
            OutputFormat::Srt => "-osrt",
            OutputFormat::Vtt => "-ovtt",
        }
    }
}

/// 处理统计信息
#[derive(Debug, Default)]
struct ProcessStats {
    total_files: usize,
    processed_files: usize,
    successful_files: usize,
    failed_files: usize,
    start_time: Option<Instant>,
    total_duration: Duration,
    failed_files_list: Vec<(PathBuf, String)>, // (文件路径, 失败原因)
    retry_success_list: Vec<(PathBuf, String)>, // (文件路径, 重试成功的模式)
}

impl ProcessStats {
    fn new(total_files: usize) -> Self {
        Self {
            total_files,
            start_time: Some(Instant::now()),
            ..Default::default()
        }
    }

    fn record_success(&mut self, duration: Duration) {
        self.processed_files += 1;
        self.successful_files += 1;
        self.total_duration += duration;
    }

    fn record_failure(&mut self, duration: Duration, file_path: PathBuf, reason: String) {
        self.processed_files += 1;
        self.failed_files += 1;
        self.total_duration += duration;
        self.failed_files_list.push((file_path, reason));
    }

    fn record_retry_success(&mut self, duration: Duration, file_path: PathBuf, retry_mode: String) {
        self.processed_files += 1;
        self.successful_files += 1;
        self.total_duration += duration;
        self.retry_success_list.push((file_path, retry_mode));
    }

    fn estimated_remaining_time(&self) -> Option<Duration> {
        if self.processed_files == 0 {
            return None;
        }

        let avg_time_per_file = self.total_duration / self.processed_files as u32;
        let remaining_files = self.total_files - self.processed_files;
        Some(avg_time_per_file * remaining_files as u32)
    }

    fn print_summary(&self) {
        let total_time = self.start_time.map(|start| start.elapsed()).unwrap_or(Duration::ZERO);

        println!("\n📊 处理统计报告");
        println!("--------------------------------------------------");
        println!("总文件数: {}", self.total_files);
        println!("成功处理: {} 个", self.successful_files);
        println!("处理失败: {} 个", self.failed_files);
        println!("总耗时: {:.2} 秒", total_time.as_secs_f64());
        if self.processed_files > 0 {
            println!("平均每文件: {:.2} 秒", self.total_duration.as_secs_f64() / self.processed_files as f64);
        }
        println!("--------------------------------------------------");
    }
}

/// 自定义错误类型，用于更好的错误处理和用户友好的错误消息
#[derive(Debug)]
enum ProcessError {
    Io(std::io::Error),
    Dialog(dialoguer::Error),
    WalkDir(walkdir::Error),
    InvalidPath(String),
    CommandFailed(String),
    FileNotFound(String),
    PermissionDenied(String),
}

impl fmt::Display for ProcessError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ProcessError::Io(err) => write!(f, "文件系统错误: {err}"),
            ProcessError::Dialog(err) => write!(f, "用户输入错误: {err}"),
            ProcessError::WalkDir(err) => write!(f, "目录遍历错误: {err}"),
            ProcessError::InvalidPath(path) => write!(f, "无效路径: {path}"),
            ProcessError::CommandFailed(cmd) => write!(f, "命令执行失败: {cmd}"),
            ProcessError::FileNotFound(file) => write!(f, "文件未找到: {file}"),
            ProcessError::PermissionDenied(path) => write!(f, "权限不足，无法访问: {path}"),
        }
    }
}

impl std::error::Error for ProcessError {}

impl From<std::io::Error> for ProcessError {
    fn from(err: std::io::Error) -> Self {
        ProcessError::Io(err)
    }
}

impl From<dialoguer::Error> for ProcessError {
    fn from(err: dialoguer::Error) -> Self {
        ProcessError::Dialog(err)
    }
}

impl From<walkdir::Error> for ProcessError {
    fn from(err: walkdir::Error) -> Self {
        ProcessError::WalkDir(err)
    }
}

/// 将质量预设转换为内部质量设置 (Convert quality preset to internal quality setting)
impl From<QualityPreset> for QualitySetting {
    fn from(preset: QualityPreset) -> Self {
        match preset {
            QualityPreset::Fast => QualitySetting::Fast,
            QualityPreset::Balanced => QualitySetting::Balanced,
            QualityPreset::HighFidelity => QualitySetting::HighQuality,
        }
    }
}

/// 加载配置文件 (Load configuration file)
fn load_config() -> Config {
    let config_path = get_config_path();

    if config_path.exists() {
        match fs::read_to_string(&config_path) {
            Ok(content) => {
                match toml::from_str::<Config>(&content) {
                    Ok(config) => {
                        info!("已加载配置文件: {}", config_path.display());
                        return config;
                    }
                    Err(e) => {
                        warn!("配置文件解析失败: {} - {}", config_path.display(), e);
                    }
                }
            }
            Err(e) => {
                warn!("无法读取配置文件: {} - {}", config_path.display(), e);
            }
        }
    }

    // 如果配置文件不存在或加载失败，使用默认配置
    let default_config = Config::default();

    // 尝试创建默认配置文件
    if let Err(e) = save_default_config(&config_path, &default_config) {
        warn!("无法创建默认配置文件: {}", e);
    }

    default_config
}

/// 获取配置文件路径 (Get configuration file path)
fn get_config_path() -> PathBuf {
    if let Some(config_dir) = dirs::config_dir() {
        let whisper_config_dir = config_dir.join("whisper-cli");
        if !whisper_config_dir.exists() {
            let _ = fs::create_dir_all(&whisper_config_dir);
        }
        whisper_config_dir.join("config.toml")
    } else {
        // 如果无法获取配置目录，使用当前目录
        PathBuf::from("config.toml")
    }
}

/// 保存默认配置文件 (Save default configuration file)
fn save_default_config(path: &Path, config: &Config) -> Result<(), Box<dyn std::error::Error>> {
    let toml_content = toml::to_string_pretty(config)?;
    fs::write(path, toml_content)?;
    info!("已创建默认配置文件: {}", path.display());
    Ok(())
}

/// 从CLI参数和配置文件创建处理配置 (Create processing config from CLI arguments and config file)
///
/// 优先级层次 (Priority hierarchy):
/// 1. 命令行参数 (CLI arguments) - 最高优先级
/// 2. 配置文件设置 (config.toml settings) - 中等优先级
/// 3. 应用默认值 (Application defaults) - 最低优先级
fn create_config_from_args(args: &Args, file_config: &Config) -> Result<ProcessConfig, ProcessError> {
    let mut config = ProcessConfig::default();

    // 设置质量模式 (优先级: CLI > config.toml > default)
    if let Some(quality) = &args.quality {
        config.quality_setting = quality.clone().into();
    } else if let Some(quality_str) = &file_config.default_quality {
        config.quality_setting = match quality_str.as_str() {
            "fast" => QualitySetting::Fast,
            "balanced" => QualitySetting::Balanced,
            "high-fidelity" => QualitySetting::HighQuality,
            _ => QualitySetting::Balanced, // 默认值
        };
    }

    // 设置输出格式 (优先级: CLI > config.toml > default)
    if let Some(format) = &args.format {
        config.output_format = format.clone();
    } else if let Some(format_str) = &file_config.default_format {
        config.output_format = match format_str.as_str() {
            "txt" => OutputFormat::Txt,
            "srt" => OutputFormat::Srt,
            "vtt" => OutputFormat::Vtt,
            _ => OutputFormat::Srt, // 默认值
        };
    }

    // 设置语言 (优先级: CLI > config.toml > default)
    if let Some(language) = &args.language {
        config.language = language.clone();
    } else if let Some(language) = &file_config.language {
        config.language = language.clone();
    }

    // 设置模型路径 (优先级: CLI > config.toml > default)
    if let Some(model) = &args.model {
        config.model_path = PathBuf::from(model.clone());
    } else if let Some(model_path) = &file_config.model_path {
        config.model_path = PathBuf::from(model_path.clone());
    } else {
        // 使用默认模型路径
        config.model_path = PathBuf::from(get_default_model_path()?);
    }

    Ok(config)
}

/// 获取默认模型路径 (Get default model path)
fn get_default_model_path() -> Result<String, ProcessError> {
    let home_dir = std::env::var("HOME").map_err(|_| {
        ProcessError::InvalidPath("无法获取HOME环境变量".to_string())
    })?;

    let model_path = format!("{}/Documents/whisper_models/ggml-large-v3.bin", home_dir);

    if !std::path::Path::new(&model_path).exists() {
        return Err(ProcessError::FileNotFound(format!(
            "模型文件不存在: {}。请下载模型文件或使用 --model 参数指定路径",
            model_path
        )));
    }

    Ok(model_path)
}

/// 交互式获取输入目录 (Interactive input directory selection)
fn get_input_directory() -> Result<PathBuf, ProcessError> {
    let root_dir_str = Input::with_theme(&ColorfulTheme::default())
        .with_prompt("请输入要扫描的根文件夹绝对路径")
        .validate_with(|input: &String| -> Result<(), &str> {
            let path = Path::new(input);
            if !path.exists() {
                return Err("目录不存在，请检查路径是否正确。");
            }
            if !path.is_dir() {
                return Err("提供的路径不是一个目录，请提供目录路径。");
            }
            // 简单的权限检查 - 尝试读取目录
            if std::fs::read_dir(path).is_err() {
                return Err("无法读取目录，请检查权限设置。");
            }
            Ok(())
        })
        .interact_text()?;

    Ok(PathBuf::from(root_dir_str))
}

/// 运行批量处理的主要逻辑 (Main batch processing logic)
fn run_batch_processing(input_dir: PathBuf, config: ProcessConfig, silent: bool) -> Result<(), ProcessError> {
    if !silent {
        println!("🚀 Whisper.cpp 批量处理程序已启动 🚀");
        info!("程序启动");
    }

    let root_dir = input_dir;

    // 检查必要的命令是否可用
    if !silent {
        println!("\n🔧 正在检查系统依赖...");
    }
    check_command_availability("ffmpeg", "FFmpeg 用于音频格式转换")?;
    check_command_availability("whisper-cli", "Whisper.cpp 用于语音识别")?;
    if !silent {
        println!("✅ 系统依赖检查完成！");
    }

    // 2. 递归扫描所有支持的音频文件
    if !silent {
        println!("\n🔍 正在扫描文件夹，请稍候...");
    }
    let audio_files: Vec<PathBuf> = WalkDir::new(&root_dir)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| {
            e.path().is_file() &&
            e.path().extension().is_some_and(|ext| {
                let ext_str = ext.to_string_lossy().to_lowercase();
                config.audio_extensions.contains(&ext_str)
            })
        })
        .map(|e| e.into_path())
        .collect();

    if audio_files.is_empty() {
        if !silent {
            println!("🤷 在指定目录下没有找到任何支持的音频文件。");
            println!("支持的格式: {}", config.audio_extensions.join(", "));
        }
        return Ok(());
    }

    if !silent {
        println!("✅ 扫描完成！共找到 {} 个音频文件。", audio_files.len());
    }

    // 初始化统计信息和进度条
    let mut stats = ProcessStats::new(audio_files.len());
    let progress_bar = ProgressBar::new(audio_files.len() as u64);
    progress_bar.set_style(
        ProgressStyle::default_bar()
            .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({percent}%) {msg}")
            .unwrap()
            .progress_chars("█▉▊▋▌▍▎▏  ")
    );

    println!("--------------------------------------------------");

    // 3. 单线程串行处理每一个文件
    for (index, audio_path) in audio_files.iter().enumerate() {
        let file_name = audio_path.file_name().unwrap_or_default().to_string_lossy();
        progress_bar.set_message(format!("处理: {file_name}"));

        let start_time = Instant::now();
        info!("开始处理文件: {}", audio_path.display());

        match process_single_file(audio_path, &config) {
            Ok(_) => {
                let duration = start_time.elapsed();
                stats.record_success(duration);
                info!("文件处理成功: {} (耗时: {:.2}秒)", file_name, duration.as_secs_f64());
                progress_bar.println(format!("✅ [{}/{}] 处理成功: {}", index + 1, audio_files.len(), file_name));
            },
            Err(e) => {
                let _initial_duration = start_time.elapsed();
                let error_msg = format!("{e}");

                // 尝试自动重试机制
                progress_bar.println(format!("⚠️  [{}/{}] 初次处理失败: {} - {}, 尝试自动重试...", index + 1, audio_files.len(), file_name, error_msg));

                match intelligent_retry_with_hallucination_detection(audio_path, &config) {
                    Ok(retry_mode) => {
                        let total_duration = start_time.elapsed();
                        stats.record_retry_success(total_duration, audio_path.clone(), retry_mode.clone());
                        info!("文件重试成功: {} 使用模式: {} (总耗时: {:.2}秒)", file_name, retry_mode, total_duration.as_secs_f64());
                        progress_bar.println(format!("🔄 [{}/{}] 重试成功: {} (使用{}模式)", index + 1, audio_files.len(), file_name, retry_mode));
                    },
                    Err(retry_error) => {
                        let total_duration = start_time.elapsed();
                        let final_error_msg = format!("初次失败: {} | 重试失败: {}", error_msg, retry_error);
                        stats.record_failure(total_duration, audio_path.clone(), final_error_msg.clone());
                        error!("文件处理完全失败: {} - {} (总耗时: {:.2}秒)", file_name, final_error_msg, total_duration.as_secs_f64());
                        progress_bar.println(format!("❌ [{}/{}] 完全失败: {} - 已尝试所有重试模式", index + 1, audio_files.len(), file_name));
                    }
                }
            }
        }

        progress_bar.inc(1);

        // 显示预估剩余时间
        if let Some(remaining_time) = stats.estimated_remaining_time() {
            progress_bar.set_message(format!("预估剩余: {:.0}秒", remaining_time.as_secs_f64()));
        }
    }

    progress_bar.finish_with_message("处理完成！");

    // 显示统计报告
    stats.print_summary();

    // 生成日志报告和失败文件管理
    generate_log_report(&stats, &root_dir)?;

    // 处理失败文件
    if !stats.failed_files_list.is_empty() {
        manage_failed_files(&stats, &root_dir)?;
    }

    info!("程序完成，共处理 {} 个文件，成功 {} 个，失败 {} 个",
          stats.total_files, stats.successful_files, stats.failed_files);

    println!("\n🎉 所有任务已完成！");
    Ok(())
}

/// 处理单个音频文件（带自定义参数）
fn process_single_file_with_custom_args(audio_path: &Path, config: &ProcessConfig, custom_args: Vec<&str>) -> Result<Vec<PathBuf>, ProcessError> {
    let file_stem = audio_path.file_stem()
        .ok_or_else(|| ProcessError::InvalidPath("无法获取文件名".to_string()))?
        .to_string_lossy();

    let output_dir = audio_path.parent()
        .ok_or_else(|| ProcessError::InvalidPath("无法获取父目录".to_string()))?;

    let script_dir = output_dir.join("script");
    fs::create_dir_all(&script_dir)?;

    let base_output_path = script_dir.join(&*file_stem);

    // 构建whisper命令
    let mut cmd = Command::new("whisper-cli");

    // 设置环境变量强制UTF-8编码输出，解决中文编码问题
    cmd.env("LC_ALL", "en_US.UTF-8")              // 强制UTF-8 locale
       .env("LANG", "en_US.UTF-8")                // 设置语言环境
       .arg("-m").arg(&config.model_path)
       .arg("-l").arg(&config.language);

    // 添加自定义参数
    for arg in custom_args {
        cmd.arg(arg);
    }

    // 添加输出格式 - 默认生成SRT用于幻觉检测
    cmd.arg("-osrt");

    cmd.arg("-of").arg(&base_output_path)
       .arg(audio_path);

    // 执行命令
    let output = cmd.output()?;

    if !output.status.success() {
        let error_msg = String::from_utf8_lossy(&output.stderr);
        return Err(ProcessError::CommandFailed(format!("whisper-cli 执行失败: {}", error_msg)));
    }

    // 收集生成的文件 - 主要收集SRT文件用于幻觉检测
    let mut output_files = Vec::new();
    let extensions = vec!["srt"]; // 专注于SRT文件

    for ext in extensions {
        let output_file = script_dir.join(format!("{}.{}", file_stem, ext));
        if output_file.exists() {
            output_files.push(output_file);
        }
    }

    Ok(output_files)
}

/// 处理单个音频文件的完整流程（带幻觉检测）
fn process_single_file(audio_path: &Path, config: &ProcessConfig) -> Result<(), ProcessError> {
    // ---- 路径准备 ----
    let parent_dir = audio_path.parent().ok_or_else(|| ProcessError::InvalidPath("无法获取父目录".to_string()))?;
    let file_stem = audio_path.file_stem()
        .ok_or_else(|| ProcessError::InvalidPath("无法获取文件名".to_string()))?
        .to_str()
        .ok_or_else(|| ProcessError::InvalidPath("文件名包含无效字符".to_string()))?;

    // 输出文件夹路径
    let script_dir = parent_dir.join("script");
    fs::create_dir_all(&script_dir)?;

    // 最终 txt 输出路径 (不含扩展名，whisper会自己加)
    let output_txt_path = script_dir.join(file_stem);

    // ---- 步骤 1: 智能音频格式处理 ----
    let wav_path = if audio_path.extension()
        .and_then(|ext| ext.to_str())
        .map(|ext| ext.to_lowercase())
        .as_deref() == Some("wav")
    {
        // 如果输入已经是WAV格式，直接使用
        println!("  - 步骤 1: 检测到WAV格式，直接使用原文件...");
        println!("    -> 使用音频文件: {}", audio_path.display());
        audio_path.to_path_buf()
    } else {
        // 需要转换为WAV格式
        let wav_path = parent_dir.join(format!("{file_stem}.wav"));
        println!("  - 步骤 1: 正在转换音频格式...");
        let ffmpeg_status = Command::new("ffmpeg")
            .arg("-i").arg(audio_path)
            .arg("-ar").arg(config.sample_rate.to_string()) // 采样率
            .arg("-ac").arg(config.channels.to_string())    // 声道数
            .arg("-c:a").arg("pcm_s16le") // 16-bit PCM
            .arg("-y") // 覆盖已存在的文件
            .arg(&wav_path)
            .stdout(std::process::Stdio::null()) // 隐藏 ffmpeg 的啰嗦输出
            .stderr(std::process::Stdio::null())
            .status()?;

        if !ffmpeg_status.success() {
            return Err(ProcessError::CommandFailed("ffmpeg 音频转换失败".to_string()));
        }
        println!("    -> 音频已转换为: {}", wav_path.display());
        wav_path
    };

    // ---- 步骤 2: 调用 whisper.cpp 进行识别 ----
    println!("  - 步骤 2: 正在调用 Whisper.cpp 进行语音识别...");
    let mut whisper_cmd = Command::new("whisper-cli");

    // 设置环境变量强制UTF-8编码输出，解决中文编码问题
    whisper_cmd
        .env("LC_ALL", "en_US.UTF-8")              // 强制UTF-8 locale
        .env("LANG", "en_US.UTF-8")                // 设置语言环境
        .arg("-m").arg(&config.model_path)         // 模型文件
        .arg("-l").arg(&config.language)           // 使用配置的语言
        .arg(config.output_format.whisper_arg())   // 使用配置的输出格式
        .arg("-of").arg(&output_txt_path);         // 指定输出文件路径(不含扩展名)

    // 添加质量设置参数
    for arg in config.quality_setting.whisper_args() {
        whisper_cmd.arg(arg);
    }

    // 输入文件作为最后参数
    whisper_cmd.arg(&wav_path);

    let whisper_status = whisper_cmd.status()?;

    if !whisper_status.success() {
        // 在返回错误前，仍然尝试清理WAV文件
        let _ = fs::remove_file(&wav_path);
        return Err(ProcessError::CommandFailed("whisper.cpp 识别失败".to_string()));
    }
    println!("    -> 识别结果已保存至: {}.{}", output_txt_path.display(), config.output_format.as_str());

    // ---- 步骤 3: 清理临时文件 ----
    // 只有当wav_path不是原始输入文件时才删除（即我们创建了临时转换文件）
    if wav_path != audio_path {
        println!("  - 步骤 3: 正在清理临时文件...");
        fs::remove_file(&wav_path)?;
        println!("    -> 已删除: {}", wav_path.display());
    } else {
        println!("  - 步骤 3: 使用原始WAV文件，无需清理临时文件");
    }

    // ---- 步骤 4: 检查生成的SRT文件是否有幻觉问题 ----
    // 总是检查SRT文件，因为我们需要检测幻觉问题
    let srt_path = script_dir.join(format!("{}.srt", file_stem));
    if srt_path.exists() {
        println!("  - 步骤 4: 正在检查重复内容...");
        match detect_hallucination_in_srt(&srt_path) {
            Ok(detection) => {
                if detection.has_repetition {
                    println!("    -> ⚠️ 检测到重复内容: {} - {}", detection.repetition_type, detection.details);
                    return Err(ProcessError::CommandFailed(format!("检测到幻觉问题: {} - {}", detection.repetition_type, detection.details)));
                } else {
                    println!("    -> ✅ 未检测到重复内容");
                }
            },
            Err(e) => {
                warn!("无法检测幻觉问题: {} - {}", srt_path.display(), e);
                // 如果检测失败，不阻止处理继续
            }
        }
    } else {
        println!("  - 步骤 4: 未找到SRT文件，跳过幻觉检测");
    }

    Ok(())
}

/// 获取用户配置选项 (Get user configuration options)
/// 使用配置文件作为默认值基础 (Use config file as default value base)
fn get_process_config(file_config: &Config) -> Result<ProcessConfig, ProcessError> {
    println!("\n⚙️  配置处理选项");

    let mut config = ProcessConfig::default();

    // 应用配置文件中的设置作为默认值
    if let Some(language) = &file_config.language {
        config.language = language.clone();
    }
    if let Some(format_str) = &file_config.default_format {
        config.output_format = match format_str.as_str() {
            "txt" => OutputFormat::Txt,
            "srt" => OutputFormat::Srt,
            "vtt" => OutputFormat::Vtt,
            _ => OutputFormat::Srt,
        };
    }
    if let Some(quality_str) = &file_config.default_quality {
        config.quality_setting = match quality_str.as_str() {
            "fast" => QualitySetting::Fast,
            "balanced" => QualitySetting::Balanced,
            "high-fidelity" => QualitySetting::HighQuality,
            _ => QualitySetting::Balanced,
        };
    }
    if let Some(model_path) = &file_config.model_path {
        config.model_path = PathBuf::from(model_path);
    }

    // 验证默认模型文件是否存在
    if !config.model_path.exists() {
        println!("⚠️  默认模型文件不存在: {}", config.model_path.display());
        let model_path_str = Input::with_theme(&ColorfulTheme::default())
            .with_prompt("请输入 Whisper 模型文件 (.bin) 的绝对路径")
            .validate_with(|input: &String| -> Result<(), &str> {
                let path = Path::new(input);
                if !path.exists() {
                    return Err("文件不存在，请检查路径是否正确。");
                }
                if !path.is_file() {
                    return Err("提供的路径不是一个文件，请提供文件路径。");
                }
                if !input.ends_with(".bin") {
                    return Err("模型文件必须是 .bin 格式，请提供正确的模型文件。");
                }
                Ok(())
            })
            .interact_text()?;
        config.model_path = PathBuf::from(model_path_str);
    } else {
        println!("✅ 使用默认模型文件: {}", config.model_path.display());
    }

    // 选择质量/速度设置 - 简化为3个清晰的预设
    let quality_options = vec![
        "快速 (最大速度，适合快速草稿)",
        "平衡 (速度与质量平衡，推荐日常使用)",
        "高保真 (最高质量，适合重要音频)"
    ];
    let quality_selection = Select::with_theme(&ColorfulTheme::default())
        .with_prompt("请选择处理质量预设")
        .default(1) // 默认选择平衡模式
        .items(&quality_options)
        .interact()?;

    config.quality_setting = match quality_selection {
        0 => QualitySetting::Fast,
        1 => QualitySetting::Balanced,
        2 => QualitySetting::HighQuality,
        _ => QualitySetting::Balanced,
    };

    // 选择输出格式
    let output_formats = vec!["TXT (纯文本)", "SRT (字幕)", "VTT (WebVTT字幕)"];
    let selection = Select::with_theme(&ColorfulTheme::default())
        .with_prompt("请选择输出格式")
        .default(0)
        .items(&output_formats)
        .interact()?;

    config.output_format = match selection {
        0 => OutputFormat::Txt,
        1 => OutputFormat::Srt,
        2 => OutputFormat::Vtt,
        _ => OutputFormat::Txt,
    };

    // 选择语言
    let languages = [
        ("中文", "zh"),
        ("英文", "en"),
        ("日文", "ja"),
        ("韩文", "ko"),
        ("自动检测", "auto"),
    ];
    let lang_names: Vec<&str> = languages.iter().map(|(name, _)| *name).collect();
    let lang_selection = Select::with_theme(&ColorfulTheme::default())
        .with_prompt("请选择语音识别语言")
        .default(0) // 默认选择中文
        .items(&lang_names)
        .interact()?;

    config.language = languages[lang_selection].1.to_string();

    println!("\n✅ 配置完成！");
    println!("  - 模型文件: {}", config.model_path.display());
    println!("  - 质量设置: {}", config.quality_setting.as_str());
    println!("  - 输出格式: {}", config.output_format.as_str().to_uppercase());
    println!("  - 识别语言: {}", config.language);
    println!("  - 支持格式: {}", config.audio_extensions.join(", "));

    Ok(config)
}

/// 检查指定命令是否在系统中可用
fn check_command_availability(command: &str, description: &str) -> Result<(), ProcessError> {
    println!("  - 检查 {command}: {description}");

    let output = Command::new("which")
        .arg(command)
        .output();

    match output {
        Ok(result) if result.status.success() => {
            println!("    ✅ {command} 已找到");
            Ok(())
        }
        _ => {
            Err(ProcessError::CommandFailed(format!(
                "{command} 命令未找到。请确保已安装 {description} 并且在 PATH 中可用。"
            )))
        }
    }
}

/// 初始化日志系统
fn init_logger() -> Result<(), ProcessError> {
    use std::env;

    // 设置默认日志级别
    if env::var("RUST_LOG").is_err() {
        env::set_var("RUST_LOG", "info");
    }

    // 初始化日志记录器
    env_logger::Builder::from_default_env()
        .format(|buf, record| {
            writeln!(buf, "{} [{}] - {}",
                Local::now().format("%Y-%m-%d %H:%M:%S"),
                record.level(),
                record.args()
            )
        })
        .init();

    Ok(())
}

/// 生成日志报告文件
fn generate_log_report(stats: &ProcessStats, root_dir: &Path) -> Result<(), ProcessError> {
    let timestamp = Local::now().format("%Y%m%d_%H%M%S");

    // 在根目录的script文件夹下创建reports目录
    let script_dir = root_dir.join("script");
    let reports_dir = script_dir.join("reports");
    fs::create_dir_all(&reports_dir)?;

    let report_path = reports_dir.join(format!("whisper_batch_report_{timestamp}.txt"));

    let mut report = String::new();
    report.push_str(&format!("Whisper 批量处理报告\n"));
    report.push_str(&format!("生成时间: {}\n", Local::now().format("%Y-%m-%d %H:%M:%S")));
    report.push_str(&format!("==========================================\n\n"));

    report.push_str(&format!("处理统计:\n"));
    report.push_str(&format!("- 总文件数: {}\n", stats.total_files));
    report.push_str(&format!("- 成功处理: {}\n", stats.successful_files));
    report.push_str(&format!("- 处理失败: {}\n", stats.failed_files));

    if let Some(start_time) = stats.start_time {
        let total_time = start_time.elapsed();
        report.push_str(&format!("- 总耗时: {:.2} 秒\n", total_time.as_secs_f64()));
    }

    if stats.processed_files > 0 {
        report.push_str(&format!("- 平均每文件: {:.2} 秒\n",
            stats.total_duration.as_secs_f64() / stats.processed_files as f64));
    }

    report.push_str(&format!("\n成功率: {:.1}%\n",
        (stats.successful_files as f64 / stats.total_files as f64) * 100.0));

    // 添加失败文件详情
    if !stats.failed_files_list.is_empty() {
        report.push_str(&format!("\n失败文件详情:\n"));
        for (i, (file_path, reason)) in stats.failed_files_list.iter().enumerate() {
            report.push_str(&format!("{}. {}\n   原因: {}\n",
                i + 1, file_path.display(), reason));
        }
    }

    // 添加重试成功文件详情
    if !stats.retry_success_list.is_empty() {
        report.push_str(&format!("\n重试成功文件详情:\n"));
        for (i, (file_path, retry_mode)) in stats.retry_success_list.iter().enumerate() {
            report.push_str(&format!("{}. {}\n   重试模式: {}\n",
                i + 1, file_path.display(), retry_mode));
        }
    }

    fs::write(&report_path, report)?;
    info!("处理报告已保存至: {}", report_path.display());
    println!("📄 处理报告已保存至: {}", report_path.display());

    Ok(())
}

/// 使用编码检测读取SRT文件，解决whisper-cli编码输出问题
///
/// whisper-cli输出UTF-8编码的中文，但可能被错误地以其他编码保存
/// 此函数尝试多种编码方式读取，确保中文字符正确显示
fn read_srt_with_encoding_detection(srt_path: &Path) -> Result<String, ProcessError> {
    // 尝试的编码顺序：UTF-8 -> GBK -> GB2312 -> Latin1
    let encodings = ["utf-8", "gbk", "gb2312", "latin1"];

    for encoding in &encodings {
        // 尝试以二进制方式读取，然后用指定编码解码
        match std::fs::read(srt_path) {
            Ok(bytes) => {
                match encoding {
                    &"utf-8" => {
                        if let Ok(content) = String::from_utf8(bytes.clone()) {
                            return Ok(content);
                        }
                    },
                    &"latin1" => {
                        // Latin1编码：每个字节直接对应一个Unicode字符
                        let content: String = bytes.iter().map(|&b| b as char).collect();
                        // 检查是否包含明显的UTF-8编码的中文字符模式
                        if content.contains("è") || content.contains("ä") || content.contains("å") {
                            // 这很可能是UTF-8编码被错误地以Latin1读取
                            // 尝试重新解码为UTF-8
                            if let Ok(utf8_content) = String::from_utf8(bytes) {
                                return Ok(utf8_content);
                            }
                        }
                        return Ok(content);
                    },
                    _ => {
                        // 对于GBK和GB2312，使用标准库无法直接处理
                        // 暂时跳过，使用Latin1作为后备
                        continue;
                    }
                }
            },
            Err(e) => return Err(ProcessError::Io(e)),
        }
    }

    // 如果所有编码都失败，使用标准方式读取
    fs::read_to_string(srt_path).map_err(ProcessError::Io)
}

/// 检测SRT文件中的重复内容和幻觉问题
fn detect_hallucination_in_srt(srt_path: &Path) -> Result<HallucinationDetection, ProcessError> {
    // 尝试多种编码方式读取文件，解决whisper-cli编码输出问题
    let content = read_srt_with_encoding_detection(srt_path)?;
    let lines: Vec<&str> = content.lines().collect();

    let mut repetition_count = HashMap::new();
    let mut short_intervals = 0;
    let mut total_intervals = 0;
    let mut consecutive_same = 0;
    let mut max_consecutive = 0;
    let mut last_text = String::new();

    // 解析SRT内容
    let mut i = 0;
    while i < lines.len() {
        // 跳过序号行
        if lines[i].trim().parse::<u32>().is_ok() {
            i += 1;
            if i >= lines.len() { break; }

            // 解析时间戳行
            if lines[i].contains("-->") {
                let time_parts: Vec<&str> = lines[i].split("-->").collect();
                if time_parts.len() == 2 {
                    let start_time = parse_srt_time(time_parts[0].trim());
                    let end_time = parse_srt_time(time_parts[1].trim());
                    let duration = end_time - start_time;

                    total_intervals += 1;
                    if duration < 1.5 { // 小于1.5秒的间隔认为异常短
                        short_intervals += 1;
                    }
                }
                i += 1;

                // 解析文本内容
                let mut text_lines = Vec::new();
                while i < lines.len() && !lines[i].trim().is_empty() {
                    text_lines.push(lines[i].trim());
                    i += 1;
                }

                let text = text_lines.join(" ").trim().to_string();
                if !text.is_empty() {
                    // 统计重复文本
                    *repetition_count.entry(text.clone()).or_insert(0) += 1;

                    // 检测连续相同文本
                    if text == last_text {
                        consecutive_same += 1;
                        max_consecutive = max_consecutive.max(consecutive_same);
                    } else {
                        consecutive_same = 0;
                    }
                    last_text = text.clone();
                }
            }
        }
        i += 1;
    }

    // 分析检测结果 - 优化检测算法，降低误判率
    let mut has_repetition = false;
    let mut repetition_type = String::new();
    let mut confidence = 0.0;
    let mut details = String::new();

    // 检测1: 连续相同短句（更严格的阈值，避免误判）
    // 只有连续5次以上相同文本才认为是严重幻觉（从3次提高到5次）
    if max_consecutive >= 5 {
        has_repetition = true;
        repetition_type = "严重连续重复".to_string();
        confidence = (max_consecutive as f64 / 15.0).min(1.0);
        details = format!("检测到{}次连续相同文本", max_consecutive);
    }

    // 检测2: 高频重复文本（更严格的条件，避免正常重复被误判）
    for (text, count) in &repetition_count {
        // 条件更严格：短文本(≤6字符)重复8次以上，或中等文本(≤15字符)重复6次以上
        let is_severe_repetition = if text.len() <= 6 {
            *count >= 8  // 很短的文本（如"嗯"、"好的"）需要8次以上
        } else if text.len() <= 15 {
            *count >= 6  // 中等长度文本需要6次以上
        } else {
            *count >= 4  // 长文本重复4次以上就可能有问题
        };

        if is_severe_repetition {
            // 排除一些常见的正常重复词汇
            let normal_repetitions = ["嗯", "啊", "哦", "是的", "好的", "对", "不是", "就是"];
            if !normal_repetitions.contains(&text.as_str()) || *count >= 10 {
                has_repetition = true;
                if repetition_type.is_empty() {
                    repetition_type = "严重高频重复".to_string();
                }
                confidence = confidence.max((*count as f64 / 25.0).min(1.0));
                details = format!("{}; 文本\"{}\"重复{}次", details, text, count);
            }
        }
    }

    // 检测3: 异常短时间间隔（更宽松的阈值，避免正常快速对话被误判）
    if total_intervals > 10 { // 至少要有10个时间间隔才进行检测
        let short_ratio = short_intervals as f64 / total_intervals as f64;
        // 提高阈值：超过50%的间隔异常短才认为有问题（从30%提高到50%）
        if short_ratio > 0.5 {
            has_repetition = true;
            if repetition_type.is_empty() {
                repetition_type = "异常短时间间隔".to_string();
            }
            confidence = confidence.max(short_ratio);
            details = format!("{}; {}%的时间间隔异常短(共{}个间隔)", details, (short_ratio * 100.0) as u32, total_intervals);
        }
    }

    Ok(HallucinationDetection {
        has_repetition,
        repetition_type,
        confidence,
        details,
    })
}

/// 解析SRT时间戳为秒数
fn parse_srt_time(time_str: &str) -> f64 {
    // 格式: 00:04:02.000
    let parts: Vec<&str> = time_str.split(':').collect();
    if parts.len() != 3 { return 0.0; }

    let hours: f64 = parts[0].parse().unwrap_or(0.0);
    let minutes: f64 = parts[1].parse().unwrap_or(0.0);
    let seconds_parts: Vec<&str> = parts[2].split('.').collect();
    let seconds: f64 = seconds_parts[0].parse().unwrap_or(0.0);
    let milliseconds: f64 = if seconds_parts.len() > 1 {
        seconds_parts[1].parse::<f64>().unwrap_or(0.0) / 1000.0
    } else { 0.0 };

    hours * 3600.0 + minutes * 60.0 + seconds + milliseconds
}

/// 智能重试机制：检测幻觉并分层重试（性能模式专用重试顺序）
fn intelligent_retry_with_hallucination_detection(audio_path: &Path, original_config: &ProcessConfig) -> Result<String, ProcessError> {
    // 重试顺序：Level3 → Level4 → Level1 → Level2 (针对性能模式优化)
    // 优先使用Level3(平衡效果和编码)，然后Level4(最强抗幻觉)，最后尝试温和模式
    let retry_levels = [
        AntiHallucinationLevel::Level3,  // 首选：极保守模式，平衡抗幻觉和编码质量
        AntiHallucinationLevel::Level4,  // 次选：分段处理模式，最强抗幻觉
        AntiHallucinationLevel::Level1,  // 备选：温度回退模式，轻度抗幻觉
        AntiHallucinationLevel::Level2,  // 最后：严格阈值模式，中度抗幻觉
    ];

    for level in retry_levels.iter() {
        info!("尝试{}重试: {}", level.description(), audio_path.display());

        // 创建重试配置
        let mut retry_config = original_config.clone();
        // 这里需要创建一个临时的质量设置来使用分层参数
        // 暂时使用AntiHallucination模式作为基础
        retry_config.quality_setting = QualitySetting::HighQuality;

        // 尝试处理
        match process_single_file_with_custom_args(audio_path, &retry_config, level.whisper_args()) {
            Ok(output_files) => {
                // 检查生成的SRT文件是否有幻觉问题
                if let Some(srt_path) = output_files.iter().find(|p| p.extension().map_or(false, |ext| ext == "srt")) {
                    match detect_hallucination_in_srt(srt_path) {
                        Ok(detection) => {
                            if !detection.has_repetition {
                                info!("{}重试成功，无幻觉检测: {}", level.description(), audio_path.display());
                                return Ok(level.description().to_string());
                            } else {
                                warn!("{}重试仍有幻觉问题: {} - {}", level.description(), detection.repetition_type, detection.details);
                                // 清理中间文件
                                for file in output_files {
                                    let _ = fs::remove_file(file);
                                }
                                continue;
                            }
                        },
                        Err(e) => {
                            warn!("无法检测幻觉问题: {} - {}", srt_path.display(), e);
                            // 如果无法检测，假设成功
                            return Ok(level.description().to_string());
                        }
                    }
                } else {
                    // 没有SRT文件，假设成功
                    return Ok(level.description().to_string());
                }
            },
            Err(e) => {
                warn!("{}重试失败: {} - {}", level.description(), audio_path.display(), e);
                continue;
            }
        }
    }

    Err(ProcessError::CommandFailed("所有分层重试都失败".to_string()))
}

/// 自动重试机制：使用更稳定的模式重试失败的文件
fn auto_retry_with_fallback_modes(audio_path: &Path, original_config: &ProcessConfig) -> Result<String, ProcessError> {
    // 定义重试模式序列：优先使用抗幻觉模式，然后是其他稳定模式
    let retry_modes = [
        ("高保真", QualitySetting::HighQuality),  // 优先选择：最高质量模式
        ("平衡", QualitySetting::Balanced),       // 次选：平衡模式
        ("快速", QualitySetting::Fast),           // 最后：基础快速模式
    ];

    for (mode_name, quality_setting) in retry_modes.iter() {
        // 跳过与原始配置相同的模式
        if std::mem::discriminant(&original_config.quality_setting) == std::mem::discriminant(quality_setting) {
            continue;
        }

        info!("尝试使用{}模式重试: {}", mode_name, audio_path.display());

        // 创建重试配置
        let mut retry_config = original_config.clone();
        retry_config.quality_setting = quality_setting.clone();

        // 尝试处理
        match process_single_file(audio_path, &retry_config) {
            Ok(_) => {
                info!("重试成功，使用{}模式: {}", mode_name, audio_path.display());
                return Ok(mode_name.to_string());
            },
            Err(e) => {
                warn!("{}模式重试失败: {} - {}", mode_name, audio_path.display(), e);
                continue;
            }
        }
    }

    Err(ProcessError::CommandFailed("所有重试模式都失败".to_string()))
}

/// 管理失败文件：创建符号链接和失败清单
fn manage_failed_files(stats: &ProcessStats, root_dir: &Path) -> Result<(), ProcessError> {
    let script_dir = root_dir.join("script");
    let failed_dir = script_dir.join("failed");
    fs::create_dir_all(&failed_dir)?;

    println!("\n🚨 处理失败文件管理...");
    info!("创建失败文件管理目录: {}", failed_dir.display());

    // 创建失败文件清单
    let failed_list_path = failed_dir.join("failed_files_list.txt");
    let mut failed_list_content = String::new();
    failed_list_content.push_str(&format!("失败文件清单\n"));
    failed_list_content.push_str(&format!("生成时间: {}\n", Local::now().format("%Y-%m-%d %H:%M:%S")));
    failed_list_content.push_str(&format!("==========================================\n\n"));

    for (i, (file_path, reason)) in stats.failed_files_list.iter().enumerate() {
        failed_list_content.push_str(&format!("{}. 文件: {}\n", i + 1, file_path.display()));
        failed_list_content.push_str(&format!("   失败原因: {}\n", reason));
        failed_list_content.push_str(&format!("   原始路径: {}\n\n", file_path.display()));

        // 创建符号链接到失败目录
        if file_path.exists() {
            let file_name = file_path.file_name().unwrap_or_default();
            let link_path = failed_dir.join(file_name);

            // 如果链接已存在，先删除
            if link_path.exists() {
                let _ = fs::remove_file(&link_path);
            }

            // 创建符号链接
            #[cfg(unix)]
            {
                use std::os::unix::fs::symlink;
                if let Err(e) = symlink(file_path, &link_path) {
                    warn!("无法创建符号链接 {} -> {}: {}", file_path.display(), link_path.display(), e);
                } else {
                    info!("创建失败文件符号链接: {}", link_path.display());
                }
            }

            #[cfg(windows)]
            {
                use std::os::windows::fs::symlink_file;
                if let Err(e) = symlink_file(file_path, &link_path) {
                    warn!("无法创建符号链接 {} -> {}: {}", file_path.display(), link_path.display(), e);
                } else {
                    info!("创建失败文件符号链接: {}", link_path.display());
                }
            }
        }
    }

    fs::write(&failed_list_path, failed_list_content)?;

    println!("📋 失败文件清单已保存至: {}", failed_list_path.display());
    println!("🔗 失败文件符号链接已创建在: {}", failed_dir.display());
    info!("失败文件管理完成");

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::path::PathBuf;
    use tempfile::TempDir;

    /// 创建测试用的临时目录和文件
    fn create_test_environment() -> (TempDir, Vec<PathBuf>) {
        let temp_dir = TempDir::new().expect("Failed to create temp dir");
        let mut audio_files = Vec::new();

        // 创建一些测试音频文件（空文件用于测试）
        let extensions = ["mp3", "wav", "m4a", "flac"];
        for (i, ext) in extensions.iter().enumerate() {
            let file_path = temp_dir.path().join(format!("test_audio_{i}.{ext}"));
            fs::write(&file_path, b"fake audio data").expect("Failed to create test file");
            audio_files.push(file_path);
        }

        // 创建一个子目录
        let sub_dir = temp_dir.path().join("subdir");
        fs::create_dir(&sub_dir).expect("Failed to create subdir");
        let sub_file = sub_dir.join("sub_audio.mp3");
        fs::write(&sub_file, b"fake audio data").expect("Failed to create sub file");
        audio_files.push(sub_file);

        (temp_dir, audio_files)
    }

    #[test]
    fn test_process_config_default() {
        let config = ProcessConfig::default();
        assert_eq!(config.sample_rate, 16000);
        assert_eq!(config.channels, 1);
        assert_eq!(config.language, "zh");
        assert!(config.audio_extensions.contains(&"mp3".to_string()));
        assert!(config.audio_extensions.contains(&"wav".to_string()));
    }

    #[test]
    fn test_output_format_methods() {
        let txt_format = OutputFormat::Txt;
        assert_eq!(txt_format.as_str(), "txt");
        assert_eq!(txt_format.whisper_arg(), "-otxt");

        let srt_format = OutputFormat::Srt;
        assert_eq!(srt_format.as_str(), "srt");
        assert_eq!(srt_format.whisper_arg(), "-osrt");

        let vtt_format = OutputFormat::Vtt;
        assert_eq!(vtt_format.as_str(), "vtt");
        assert_eq!(vtt_format.whisper_arg(), "-ovtt");
    }

    #[test]
    fn test_process_stats() {
        let mut stats = ProcessStats::new(5);
        assert_eq!(stats.total_files, 5);
        assert_eq!(stats.processed_files, 0);
        assert_eq!(stats.successful_files, 0);
        assert_eq!(stats.failed_files, 0);

        // 记录成功
        stats.record_success(Duration::from_secs(10));
        assert_eq!(stats.processed_files, 1);
        assert_eq!(stats.successful_files, 1);
        assert_eq!(stats.failed_files, 0);

        // 记录失败
        stats.record_failure(Duration::from_secs(5), PathBuf::from("test.mp3"), "Test error".to_string());
        assert_eq!(stats.processed_files, 2);
        assert_eq!(stats.successful_files, 1);
        assert_eq!(stats.failed_files, 1);

        // 测试预估时间
        let estimated = stats.estimated_remaining_time();
        assert!(estimated.is_some());
    }

    #[test]
    fn test_process_error_display() {
        let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "File not found");
        let process_error = ProcessError::Io(io_error);
        let error_msg = format!("{process_error}");
        assert!(error_msg.contains("文件系统错误"));

        let invalid_path_error = ProcessError::InvalidPath("invalid/path".to_string());
        let error_msg = format!("{invalid_path_error}");
        assert!(error_msg.contains("无效路径"));

        let command_failed_error = ProcessError::CommandFailed("ffmpeg failed".to_string());
        let error_msg = format!("{command_failed_error}");
        assert!(error_msg.contains("命令执行失败"));
    }

    #[test]
    fn test_audio_file_detection() {
        let (_temp_dir, audio_files) = create_test_environment();
        let config = ProcessConfig::default();

        // 测试音频文件扩展名检测
        for file in &audio_files {
            if let Some(ext) = file.extension() {
                let ext_str = ext.to_string_lossy().to_lowercase();
                let is_supported = config.audio_extensions.contains(&ext_str);
                assert!(is_supported, "Extension {ext_str} should be supported");
            }
        }
    }

    #[test]
    fn test_error_conversion() {
        // 测试从 std::io::Error 转换
        let io_error = std::io::Error::new(std::io::ErrorKind::PermissionDenied, "Permission denied");
        let process_error: ProcessError = io_error.into();
        match process_error {
            ProcessError::Io(_) => (),
            _ => panic!("Expected ProcessError::Io"),
        }

        // 测试从 walkdir::Error 转换
        // 注意：这里我们无法轻易创建 walkdir::Error，所以跳过这个测试

        // 测试从 dialoguer::Error 转换
        // 注意：这里我们无法轻易创建 dialoguer::Error，所以跳过这个测试
    }
}

/// 新的main函数，支持CLI参数和交互式模式 (New main function with CLI and interactive support)
fn main() -> Result<(), ProcessError> {
    // 解析命令行参数
    let args = Args::parse();

    // 初始化日志系统
    init_logger()?;

    // 加载配置文件
    let file_config = load_config();

    // 检查是否使用CLI模式
    let use_cli_mode = args.input.is_some() || args.quality.is_some() || args.format.is_some();

    if use_cli_mode {
        // CLI模式
        if !args.silent {
            println!("🎛️ 使用CLI参数模式");
        }

        // 创建配置
        let config = create_config_from_args(&args, &file_config)?;

        // 获取输入目录
        let input_dir = if let Some(input) = args.input {
            PathBuf::from(input)
        } else {
            if !args.silent {
                println!("📁 请输入音频文件目录路径:");
            }
            get_input_directory()?
        };

        // 验证输入目录
        if !input_dir.exists() {
            return Err(ProcessError::InvalidPath(format!("目录不存在: {}", input_dir.display())));
        }
        if !input_dir.is_dir() {
            return Err(ProcessError::InvalidPath(format!("路径不是目录: {}", input_dir.display())));
        }

        if !args.silent {
            println!("🔧 配置信息:");
            println!("  输入目录: {}", input_dir.display());
            println!("  质量模式: {}", config.quality_setting.as_str());
            println!("  输出格式: {:?}", config.output_format);
            println!("  语言设置: {}", config.language);
            println!("  模型路径: {}", config.model_path.display());
        }

        // 运行批量处理
        run_batch_processing(input_dir, config, args.silent)
    } else {
        // 交互式模式
        println!("🎛️ 使用交互式配置模式");

        // 获取输入目录
        let input_dir = get_input_directory()?;

        // 获取处理配置
        let config = get_process_config(&file_config)?;

        // 运行批量处理
        run_batch_processing(input_dir, config, false)
    }
}
