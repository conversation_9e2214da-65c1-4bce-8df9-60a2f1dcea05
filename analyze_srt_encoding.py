#!/usr/bin/env python3
"""
分析SRT文件的编码问题
"""

import os
import glob

def analyze_srt_files():
    print("🔍 分析SRT文件编码问题")
    print("=" * 50)
    
    srt_files = glob.glob("script/*.srt")
    print(f"📁 找到 {len(srt_files)} 个SRT文件")
    
    encoding_issues = []
    content_issues = []
    
    for i, srt_file in enumerate(srt_files[:10]):  # 检查前10个文件
        print(f"\n📄 检查文件 {i+1}: {os.path.basename(srt_file)}")
        
        try:
            # 尝试用不同编码读取
            encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'big5', 'latin1']
            content = None
            successful_encoding = None
            
            for encoding in encodings_to_try:
                try:
                    with open(srt_file, 'r', encoding=encoding) as f:
                        content = f.read(500)  # 读取前500字符
                        successful_encoding = encoding
                        break
                except UnicodeDecodeError:
                    continue
            
            if content:
                print(f"  ✅ 成功编码: {successful_encoding}")
                
                # 检查内容问题
                lines = content.split('\n')[:10]
                for line_num, line in enumerate(lines):
                    if '**' in line:
                        print(f"  ⚠️ 第{line_num+1}行发现**字符: {line}")
                        content_issues.append((srt_file, line_num+1, line))
                    
                    # 检查异常字符
                    if any(ord(c) > 127 and c not in '，。！？：；""''（）【】' for c in line if c.isalpha()):
                        unusual_chars = [c for c in line if ord(c) > 127 and c not in '，。！？：；""''（）【】' and not c.isalpha()]
                        if unusual_chars:
                            print(f"  🔍 第{line_num+1}行异常字符: {unusual_chars}")
            else:
                print(f"  ❌ 无法读取文件")
                encoding_issues.append(srt_file)
                
        except Exception as e:
            print(f"  ❌ 处理文件时出错: {e}")
            encoding_issues.append(srt_file)
    
    print("\n" + "=" * 50)
    print("📊 分析结果汇总:")
    print(f"  编码问题文件: {len(encoding_issues)}")
    print(f"  内容问题文件: {len(set(issue[0] for issue in content_issues))}")
    
    if content_issues:
        print(f"\n⚠️ 发现的内容问题:")
        for file_path, line_num, line in content_issues[:5]:  # 显示前5个问题
            print(f"  {os.path.basename(file_path)}:{line_num} - {line.strip()}")
    
    return encoding_issues, content_issues

if __name__ == "__main__":
    analyze_srt_files()
