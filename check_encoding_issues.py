#!/usr/bin/env python3
"""
检查SRT文件的编码问题并尝试修复
"""

import os
import glob
import codecs

def check_and_fix_encoding():
    print("🔍 检查SRT文件编码问题")
    print("=" * 50)
    
    srt_files = glob.glob("script/*.srt")
    print(f"📁 找到 {len(srt_files)} 个SRT文件")
    
    for i, srt_file in enumerate(srt_files):
        print(f"\n📄 检查文件 {i+1}: {os.path.basename(srt_file)}")
        
        # 尝试不同编码读取
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'latin1', 'cp1252']
        content = None
        successful_encoding = None
        
        for encoding in encodings:
            try:
                with open(srt_file, 'r', encoding=encoding) as f:
                    content = f.read()
                    successful_encoding = encoding
                    print(f"  ✅ 成功编码: {encoding}")
                    break
            except UnicodeDecodeError:
                continue
        
        if content:
            # 显示前几行内容
            lines = content.split('\n')[:10]
            print(f"  📝 内容预览:")
            for j, line in enumerate(lines):
                if line.strip():
                    print(f"    {j+1}: {line}")
                    
            # 检查是否有异常字符
            has_issues = False
            for line in lines:
                if any(ord(c) < 32 and c not in '\n\r\t' for c in line):
                    print(f"  ⚠️ 发现控制字符: {repr(line)}")
                    has_issues = True
                    
            # 如果编码不是UTF-8，尝试转换
            if successful_encoding != 'utf-8':
                try:
                    # 创建UTF-8版本
                    utf8_file = srt_file.replace('.srt', '_utf8.srt')
                    with open(utf8_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"  🔄 已创建UTF-8版本: {os.path.basename(utf8_file)}")
                except Exception as e:
                    print(f"  ❌ 转换失败: {e}")
        else:
            print(f"  ❌ 无法读取文件")
            
            # 尝试二进制读取查看原始内容
            try:
                with open(srt_file, 'rb') as f:
                    raw_data = f.read(200)
                    print(f"  🔍 原始数据 (前200字节): {raw_data}")
            except Exception as e:
                print(f"  ❌ 无法读取原始数据: {e}")

if __name__ == "__main__":
    check_and_fix_encoding()
