#!/bin/bash

echo "🎵 WAV文件处理测试脚本"
echo "======================"

# 检查编译状态
if [ ! -f "./target/release/whisper_batch_processor" ]; then
    echo "❌ 程序未编译，正在编译..."
    cargo build --release
    if [ $? -ne 0 ]; then
        echo "❌ 编译失败"
        exit 1
    fi
fi

echo "✅ 程序已编译完成"

# 创建测试目录
TEST_DIR="test_wav_files"
mkdir -p "$TEST_DIR"

echo "📁 创建测试WAV文件..."

# 创建一个简单的测试WAV文件（如果ffmpeg可用）
if command -v ffmpeg >/dev/null 2>&1; then
    # 生成一个5秒的测试音频（440Hz正弦波）
    ffmpeg -f lavfi -i "sine=frequency=440:duration=5" -ar 16000 -ac 1 -c:a pcm_s16le "$TEST_DIR/test_audio.wav" -y >/dev/null 2>&1
    
    if [ -f "$TEST_DIR/test_audio.wav" ]; then
        echo "✅ 创建测试WAV文件: $TEST_DIR/test_audio.wav"
    else
        echo "❌ 无法创建测试WAV文件"
        exit 1
    fi
else
    echo "❌ 未找到ffmpeg，无法创建测试文件"
    exit 1
fi

echo ""
echo "🚀 开始WAV文件处理测试"
echo "======================"

# 测试1: 使用CLI模式处理WAV文件
echo "📋 测试1: CLI模式处理WAV文件"
echo "命令: ./target/release/whisper_batch_processor -i $TEST_DIR -q fast -f srt -l zh --silent"
echo "------"

# 获取绝对路径
TEST_DIR_ABS="$(pwd)/$TEST_DIR"

# 运行CLI命令
./target/release/whisper_batch_processor \
    --input "$TEST_DIR_ABS" \
    --quality fast \
    --format srt \
    --language zh \
    --silent

echo ""
echo "📊 处理完成，检查结果..."

# 检查是否有错误输出
if [ $? -eq 0 ]; then
    echo "✅ 程序执行成功，无错误退出"
else
    echo "❌ 程序执行失败，退出码: $?"
fi

# 检查生成的文件
echo ""
echo "🔍 检查生成的文件:"
if [ -d "$TEST_DIR" ]; then
    echo "📁 $TEST_DIR 目录内容:"
    ls -la "$TEST_DIR"
    
    # 检查是否生成了SRT文件
    srt_count=$(find "$TEST_DIR" -name "*.srt" | wc -l)
    echo ""
    echo "生成的SRT文件数量: $srt_count"
    
    if [ $srt_count -gt 0 ]; then
        echo ""
        echo "📄 SRT文件列表:"
        find "$TEST_DIR" -name "*.srt" -exec basename {} \;
        
        echo ""
        echo "🔤 检查编码格式:"
        find "$TEST_DIR" -name "*.srt" -exec file {} \;
        
        echo ""
        echo "📝 内容预览 (前5行):"
        find "$TEST_DIR" -name "*.srt" -exec head -5 {} \;
    else
        echo "⚠️ 未生成SRT文件"
    fi
else
    echo "❌ 测试目录不存在"
fi

# 检查处理报告
echo ""
echo "📊 检查处理报告:"
if [ -d "$TEST_DIR/reports" ]; then
    report_count=$(find "$TEST_DIR/reports" -name "*.txt" | wc -l)
    echo "报告文件数量: $report_count"
    
    if [ $report_count -gt 0 ]; then
        latest_report=$(find "$TEST_DIR/reports" -name "*.txt" | head -1)
        echo ""
        echo "📋 最新报告内容:"
        echo "文件: $(basename "$latest_report")"
        echo "------"
        head -10 "$latest_report"
    fi
else
    echo "⚠️ 未找到reports目录"
fi

echo ""
echo "🎯 测试总结:"
echo "============"

# 验证关键指标
success=true

echo "🔧 WAV文件处理验证:"
if [ -f "$TEST_DIR/test_audio.wav" ]; then
    echo "  ✅ 测试WAV文件存在"
    
    # 检查文件是否被意外删除（应该保留原始WAV文件）
    if [ -f "$TEST_DIR/test_audio.wav" ]; then
        echo "  ✅ 原始WAV文件被正确保留"
    else
        echo "  ❌ 原始WAV文件被错误删除"
        success=false
    fi
else
    echo "  ❌ 测试WAV文件不存在"
    success=false
fi

echo ""
if [ "$success" = true ]; then
    echo "🎉 WAV文件处理测试通过！"
    echo "✅ WAV文件直接处理功能正常"
    echo "✅ 原始文件正确保留"
    echo "✅ 无需ffmpeg转换"
else
    echo "❌ WAV文件处理测试失败"
    echo "请检查上述错误信息"
fi

echo ""
echo "🧹 清理测试文件..."
rm -rf "$TEST_DIR"
echo "✅ 清理完成"

echo ""
echo "💡 WAV文件处理说明:"
echo "==================="
echo "• WAV文件会被直接使用，无需ffmpeg转换"
echo "• 原始WAV文件会被保留，不会被删除"
echo "• 这避免了不必要的格式转换和可能的错误"
echo "• 提高了处理速度和稳定性"
