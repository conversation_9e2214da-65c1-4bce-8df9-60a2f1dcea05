#!/bin/bash

echo "🎛️ CLI功能测试脚本"
echo "=================="

# 检查编译状态
if [ ! -f "./target/release/whisper_batch_processor" ]; then
    echo "❌ 程序未编译，正在编译..."
    cargo build --release
    if [ $? -ne 0 ]; then
        echo "❌ 编译失败"
        exit 1
    fi
fi

echo "✅ 程序已编译完成"

# 检查音频文件
if [ ! -d "script/audio" ]; then
    echo "❌ script/audio 目录不存在"
    exit 1
fi

audio_count=$(find script/audio -name "*.mp3" | wc -l)
echo "📁 找到 $audio_count 个音频文件"

if [ $audio_count -eq 0 ]; then
    echo "❌ 未找到音频文件"
    exit 1
fi

# 清理旧的输出文件
echo "🧹 清理旧输出文件..."
rm -f script/*.srt script/*.txt script/*.vtt
rm -f script/reports/*.txt

echo ""
echo "🚀 开始CLI功能测试"
echo "=================="

# 测试1: 显示帮助信息
echo "📋 测试1: 显示帮助信息"
echo "命令: ./target/release/whisper_batch_processor --help"
echo "------"
./target/release/whisper_batch_processor --help
echo ""

# 测试2: 使用CLI参数进行批量处理
echo "📋 测试2: 使用CLI参数进行批量处理"
echo "命令: ./target/release/whisper_batch_processor -i script/audio -q performance -f srt -l zh"
echo "------"

# 获取绝对路径
AUDIO_DIR="$(pwd)/script/audio"

# 运行CLI命令
./target/release/whisper_batch_processor \
    --input "$AUDIO_DIR" \
    --quality performance \
    --format srt \
    --language zh

echo ""
echo "📊 处理完成，检查结果..."

# 检查生成的文件
echo "🔍 检查生成的SRT文件:"
srt_count=$(find script -name "*.srt" | wc -l)
echo "生成的SRT文件数量: $srt_count"

if [ $srt_count -gt 0 ]; then
    echo ""
    echo "📄 SRT文件列表:"
    for srt in script/*.srt; do
        if [ -f "$srt" ]; then
            echo "  $(basename "$srt")"
        fi
    done
    
    echo ""
    echo "🔤 检查编码格式:"
    for srt in script/*.srt; do
        if [ -f "$srt" ]; then
            echo "  $(basename "$srt"): $(file "$srt")"
        fi
    done
    
    echo ""
    echo "📝 内容预览 (前3行):"
    for srt in script/*.srt; do
        if [ -f "$srt" ]; then
            echo "=== $(basename "$srt") ==="
            head -10 "$srt" | grep -v "^[0-9]*$" | grep -v "^$" | grep -v "-->" | head -3
            echo ""
        fi
    done
else
    echo "⚠️ 未生成SRT文件"
fi

# 检查处理报告
echo "📊 检查处理报告:"
if [ -d "script/reports" ]; then
    report_count=$(find script/reports -name "*.txt" | wc -l)
    echo "报告文件数量: $report_count"
    
    if [ $report_count -gt 0 ]; then
        latest_report=$(find script/reports -name "*.txt" | head -1)
        echo ""
        echo "📋 最新报告内容:"
        echo "文件: $(basename "$latest_report")"
        echo "------"
        head -20 "$latest_report"
    fi
else
    echo "⚠️ 未找到reports目录"
fi

echo ""
echo "🎯 测试总结:"
echo "============"

# 验证关键指标
success=true

# 检查编码
echo "🔤 编码验证:"
for srt in script/*.srt; do
    if [ -f "$srt" ]; then
        encoding=$(file "$srt")
        if [[ "$encoding" == *"UTF-8"* ]]; then
            echo "  ✅ $(basename "$srt"): UTF-8编码正确"
        else
            echo "  ❌ $(basename "$srt"): 编码异常 - $encoding"
            success=false
        fi
    fi
done

# 检查内容
echo ""
echo "📝 内容验证:"
for srt in script/*.srt; do
    if [ -f "$srt" ]; then
        # 检查是否有乱码字符
        if grep -q "è\|ä\|å\|ç\|ï\|ü" "$srt"; then
            echo "  ❌ $(basename "$srt"): 检测到疑似乱码字符"
            success=false
        else
            echo "  ✅ $(basename "$srt"): 内容看起来正常"
        fi
    fi
done

echo ""
if [ "$success" = true ]; then
    echo "🎉 CLI功能测试通过！"
    echo "✅ 编码问题已修复"
    echo "✅ CLI参数功能正常"
    echo "✅ 批量处理成功"
else
    echo "❌ CLI功能测试失败"
    echo "请检查上述错误信息"
fi

echo ""
echo "💡 CLI使用示例:"
echo "==============="
echo "# 基本用法"
echo "./target/release/whisper_batch_processor -i /path/to/audio -q performance -f srt -l zh"
echo ""
echo "# 静默模式"
echo "./target/release/whisper_batch_processor -i /path/to/audio -q balanced -f srt --silent"
echo ""
echo "# 指定模型"
echo "./target/release/whisper_batch_processor -i /path/to/audio -m /path/to/model.bin -f srt"
echo ""
echo "# 查看所有选项"
echo "./target/release/whisper_batch_processor --help"
