# 更新日志

## v0.1.1 - 2024-08-13

### 🔧 修复
- **Whisper CLI 兼容性**: 更新以支持最新的 `whisper-cli` 命令
  - 从 `whisper-cpp` 更改为 `whisper-cli` (适配 Homebrew 安装)
  - 移除不支持的 `--metal` 参数
  - 更新参数格式以匹配新的 CLI 接口

### ⚙️ 参数更新
- **质量设置参数**: 
  - 快速模式: `-bo 1 -bs 1` (替代 `--best-of 1 --beam-size 1`)
  - 平衡模式: `-bo 2 -bs 2` (替代 `--best-of 2 --beam-size 2`)
  - 高质量模式: `-bo 5 -bs 5` (替代 `--best-of 5 --beam-size 5`)

- **输出格式参数**:
  - TXT: `-otxt` (替代 `--output-txt`)
  - SRT: `-osrt` (替代 `--output-srt`)
  - VTT: `-ovtt` (替代 `--output-vtt`)

- **其他参数**:
  - 模型: `-m` (替代 `--model`)
  - 语言: `-l` (替代 `--language`)
  - 输出文件: `-of` (替代 `--output-file`)

### 📚 文档更新
- 更新安装指南推荐使用 `brew install whisper-cpp`
- 修正故障排除指南中的命令名称
- 更新使用示例以反映新的 CLI 接口

### ✅ 测试
- 更新单元测试以匹配新的参数格式
- 所有测试保持 100% 通过率

---

## v0.1.0 - 2024-08-13

### 🎉 初始版本
- 批量音频转录功能
- 多种音频格式支持 (MP3, WAV, M4A, FLAC)
- 多语言识别支持
- 多种输出格式 (TXT, SRT, VTT)
- 实时进度显示和统计
- 完整的日志记录系统
- 智能配置和验证
- 全面的测试套件
- 详细的文档和使用示例
