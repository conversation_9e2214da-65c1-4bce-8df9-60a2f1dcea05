# Whisper 抗幻觉优化指南

## 🎯 问题描述

当 Whisper 遇到"鸡尾酒会效应"（多人声重叠）时，会出现重复性幻觉问题：
- 模型开始无休止地输出同一个短语
- 时间戳以极短间隔重复
- 从故障点开始的所有转录内容失效

## 🔬 根本原因分析

### 技术原理
1. **多源人声重叠**: 主要说话人与背景声音强度相似
2. **信噪比极低**: 模型无法清晰区分主要音轨
3. **置信度下降**: 内部置信度分数低于阈值
4. **回退机制缺陷**: 处理非静音混乱输入时陷入循环

### 关键参数影响
- `beam-size`: 搜索宽度，影响探索vs利用平衡
- `temperature`: 随机性控制，影响输出确定性
- `logprob-thold`: 置信度要求，过低导致接受低质量输出
- `entropy-thold`: 熵阈值，控制输出的确定性要求

## 🛠️ 解决策略

### 策略1: 保守抗幻觉 (推荐首选)
```bash
whisper-cli -m model.bin \
  -l zh \
  -bo 2 -bs 2 \
  --temperature 0.0,0.2,0.4,0.6,0.8,1.0 \
  --logprob-thold -0.5 \
  --no-speech-thold 0.8 \
  --entropy-thold 3.0 \
  input.wav
```

**原理解释:**
- `beam-size 2`: 适中搜索宽度，平衡质量与稳定性
- `temperature 0.0-1.0`: 完整温度回退，从确定到随机
- `logprob-thold -0.5`: 中等置信度要求
- `no-speech-thold 0.8`: 较高静音检测阈值

### 策略2: 激进抗幻觉 (问题严重时)
```bash
whisper-cli -m model.bin \
  -l zh \
  -bo 1 -bs 1 \
  --temperature 0.2,0.4,0.6,0.8,1.0 \
  --logprob-thold -0.3 \
  --no-speech-thold 0.9 \
  --entropy-thold 2.0 \
  --word-thold 0.02 \
  --max-len 50 \
  input.wav
```

**原理解释:**
- `beam-size 1`: 最窄搜索，强制快速决策
- `temperature 0.2-1.0`: 跳过最低温度，增加随机性
- `logprob-thold -0.3`: 高置信度要求
- `max-len 50`: 限制输出长度，防止长重复

### 策略3: 鸡尾酒会专用 (多人声场景)
```bash
whisper-cli -m model.bin \
  -l zh \
  -bo 3 -bs 3 \
  --temperature 0.0,0.1,0.3,0.5,0.7,1.0 \
  --logprob-thold -0.7 \
  --no-speech-thold 0.7 \
  --entropy-thold 3.5 \
  --word-thold 0.005 \
  input.wav
```

**原理解释:**
- `beam-size 3`: 平衡的搜索宽度
- 更精细的温度控制
- 更宽松的阈值设置，适应复杂环境

### 策略4: 超保守 (最后手段)
```bash
whisper-cli -m model.bin \
  -l zh \
  -bo 1 -bs 1 \
  --temperature 0.0 \
  --logprob-thold -0.1 \
  --no-speech-thold 0.95 \
  --entropy-thold 1.5 \
  --word-thold 0.05 \
  --max-len 30 \
  input.wav
```

**原理解释:**
- 完全确定性输出
- 极高置信度要求
- 牺牲部分内容换取稳定性

## 📊 参数详解

### 核心参数
| 参数 | 作用 | 抗幻觉建议 | 说明 |
|------|------|------------|------|
| `-bo N` | best-of数量 | 1-3 | 越小越保守，越大质量越高但可能不稳定 |
| `-bs N` | beam-size | 1-3 | 搜索宽度，1最保守，5+可能导致幻觉 |
| `--temperature` | 随机性 | 0.0,0.2,0.4... | 温度回退策略，从确定到随机 |
| `--logprob-thold` | 置信度阈值 | -0.1到-0.7 | 越高要求越严格，-0.3是好的起点 |

### 辅助参数
| 参数 | 作用 | 抗幻觉建议 | 说明 |
|------|------|------------|------|
| `--entropy-thold` | 熵阈值 | 1.5-3.5 | 越低要求输出越确定 |
| `--word-thold` | 单词置信度 | 0.005-0.05 | 单词级别的置信度要求 |
| `--max-len` | 最大长度 | 30-50 | 限制输出长度，防止长重复 |
| `--no-speech-thold` | 静音阈值 | 0.7-0.95 | 越高越容易判定为静音 |

## 🧪 测试流程

### 1. 运行测试脚本
```bash
./test_whisper_modes.sh
```

### 2. 分析结果
检查输出文件：
- `01_baseline.txt` - 基础结果，确定问题位置
- `02_conservative.txt` - 保守策略结果
- `03_aggressive.txt` - 激进策略结果
- `04_cocktail_party.txt` - 鸡尾酒会策略结果
- `05_ultra_conservative.txt` - 超保守策略结果

### 3. 选择最佳策略
比较标准：
1. 重复性幻觉是否消除
2. 整体转录质量
3. 处理速度
4. 内容完整性

## 🔧 集成到批量工具

在批量处理工具中选择对应的质量设置：
- **抗幻觉模式**: 对应激进策略
- **鸡尾酒会模式**: 对应鸡尾酒会策略

## 📈 高级技巧

### 1. 分段处理
对于长音频，考虑分段处理：
```bash
# 使用 ffmpeg 分段
ffmpeg -i input.wav -f segment -segment_time 300 -c copy segment_%03d.wav
```

### 2. 预处理优化
```bash
# 音频标准化
ffmpeg -i input.wav -af "loudnorm=I=-16:TP=-1.5:LRA=11" normalized.wav

# 降噪处理
ffmpeg -i input.wav -af "afftdn=nf=-25" denoised.wav
```

### 3. 后处理检测
编写脚本检测重复模式：
```python
def detect_repetition(text, threshold=3):
    lines = text.split('\n')
    for i in range(len(lines)-threshold):
        if lines[i] == lines[i+1] == lines[i+2]:
            return i  # 返回重复开始位置
    return -1
```

## 🎯 最佳实践

1. **先测试**: 使用测试脚本找到最佳参数
2. **分层策略**: 从保守到激进逐步尝试
3. **监控输出**: 实时检查是否出现重复
4. **备用方案**: 准备多种参数组合
5. **质量评估**: 平衡稳定性与准确性

## 🚨 注意事项

- 参数调整可能影响整体转录质量
- 过于保守的设置可能丢失部分内容
- 不同音频类型可能需要不同策略
- 建议保留原始音频以便重新处理
