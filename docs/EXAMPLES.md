# 使用示例

本文档提供了 Whisper 批量音频转录工具的详细使用示例。

## 📁 目录结构示例

假设你有以下音频文件结构：
```
/Users/<USER>/audio_files/
├── meeting_2024_01_15.mp3
├── interview_john.wav
├── podcast_episode_1.m4a
├── lectures/
│   ├── lecture_01.mp3
│   ├── lecture_02.wav
│   └── lecture_03.flac
└── recordings/
    ├── voice_memo_1.mp3
    └── voice_memo_2.wav
```

## 🚀 完整使用流程

### 1. 启动程序
```bash
$ cargo run --release
🚀 Whisper.cpp 批量处理程序已启动 🚀
```

### 2. 输入根目录
```
请输入要扫描的根文件夹绝对路径: /Users/<USER>/audio_files
```

### 3. 模型文件检查
```
✅ 使用默认模型文件: /Users/<USER>/Documents/whisper_models/ggml-large-v3.bin
```
或者如果默认模型不存在：
```
⚠️  默认模型文件不存在: /Users/<USER>/Documents/whisper_models/ggml-large-v3.bin
请输入 Whisper 模型文件 (.bin) 的绝对路径: /Users/<USER>/whisper.cpp/models/ggml-medium.bin
```

### 4. 系统依赖检查
```
🔧 正在检查系统依赖...
  - 检查 ffmpeg: FFmpeg 用于音频格式转换
    ✅ ffmpeg 已找到
  - 检查 whisper-cli: Whisper.cpp 用于语音识别
    ✅ whisper-cli 已找到
✅ 系统依赖检查完成！
```

### 5. 配置处理选项
```
⚙️  配置处理选项
? 请选择处理质量/速度设置 ›
  快速 (速度优先)
❯ 平衡 (速度与质量平衡)
  高质量 (质量优先)

? 请选择输出格式 ›
❯ TXT (纯文本)
  SRT (字幕)
  VTT (WebVTT字幕)

? 请选择语音识别语言 ›
❯ 中文
  英文
  日文
  韩文
  自动检测

✅ 配置完成！
  - 模型文件: /Users/<USER>/Documents/whisper_models/ggml-large-v3.bin
  - 质量设置: 平衡
  - 输出格式: TXT
  - 识别语言: zh
  - 支持格式: mp3, wav, m4a, flac
```

### 6. 文件扫描
```
🔍 正在扫描文件夹，请稍候...
✅ 扫描完成！共找到 8 个音频文件。
支持的格式: mp3, wav, m4a, flac
```

### 7. 批量处理过程
```
--------------------------------------------------
⠁ [00:00:00] [████████████████████████████████████████] 8/8 (100%) 预估剩余: 0秒
✅ [1/8] 处理成功: meeting_2024_01_15.mp3
✅ [2/8] 处理成功: interview_john.wav
✅ [3/8] 处理成功: podcast_episode_1.m4a
✅ [4/8] 处理成功: lecture_01.mp3
✅ [5/8] 处理成功: lecture_02.wav
✅ [6/8] 处理成功: lecture_03.flac
✅ [7/8] 处理成功: voice_memo_1.mp3
✅ [8/8] 处理成功: voice_memo_2.wav
处理完成！
```

### 8. 处理统计报告
```
📊 处理统计报告
--------------------------------------------------
总文件数: 8
成功处理: 8 个
处理失败: 0 个
总耗时: 245.67 秒
平均每文件: 30.71 秒
--------------------------------------------------

📄 处理报告已保存至: whisper_batch_report_20240115_143022.txt

🎉 所有任务已完成！
```

## 📁 处理后的目录结构

```
/Users/<USER>/audio_files/
├── meeting_2024_01_15.mp3
├── interview_john.wav
├── podcast_episode_1.m4a
├── script/
│   ├── meeting_2024_01_15.txt      # 新生成的转录文件
│   ├── interview_john.txt
│   └── podcast_episode_1.txt
├── lectures/
│   ├── lecture_01.mp3
│   ├── lecture_02.wav
│   ├── lecture_03.flac
│   └── script/
│       ├── lecture_01.txt          # 新生成的转录文件
│       ├── lecture_02.txt
│       └── lecture_03.txt
└── recordings/
    ├── voice_memo_1.mp3
    ├── voice_memo_2.wav
    └── script/
        ├── voice_memo_1.txt        # 新生成的转录文件
        └── voice_memo_2.txt
```

## 📝 输出文件示例

### TXT 格式输出 (meeting_2024_01_15.txt)
```
大家好，欢迎参加今天的项目会议。今天我们主要讨论三个议题：
第一，项目进度汇报；
第二，遇到的技术难题；
第三，下一阶段的工作安排。

首先，让我们来看看项目的整体进度...
```

### SRT 字幕格式输出 (meeting_2024_01_15.srt)
```
1
00:00:00,000 --> 00:00:05,000
大家好，欢迎参加今天的项目会议。

2
00:00:05,000 --> 00:00:12,000
今天我们主要讨论三个议题：第一，项目进度汇报；

3
00:00:12,000 --> 00:00:18,000
第二，遇到的技术难题；第三，下一阶段的工作安排。
```

### VTT 字幕格式输出 (meeting_2024_01_15.vtt)
```
WEBVTT

00:00:00.000 --> 00:00:05.000
大家好，欢迎参加今天的项目会议。

00:00:05.000 --> 00:00:12.000
今天我们主要讨论三个议题：第一，项目进度汇报；

00:00:12.000 --> 00:00:18.000
第二，遇到的技术难题；第三，下一阶段的工作安排。
```

## 📊 处理报告示例

### whisper_batch_report_20240115_143022.txt
```
Whisper 批量处理报告
生成时间: 2024-01-15 14:30:22
==========================================

处理统计:
- 总文件数: 8
- 成功处理: 8
- 处理失败: 0
- 总耗时: 245.67 秒
- 平均每文件: 30.71 秒

成功率: 100.0%
```

## 🔧 高级使用技巧

### 1. 环境变量配置
```bash
# 设置日志级别
export RUST_LOG=debug

# 运行程序
cargo run --release
```

### 2. 批处理脚本
创建一个批处理脚本 `batch_process.sh`：
```bash
#!/bin/bash
export RUST_LOG=info
cd /path/to/whisper_batch_processor
./target/release/whisper_batch_processor
```

### 3. 定时任务
使用 cron 定时处理：
```bash
# 编辑 crontab
crontab -e

# 添加定时任务（每天凌晨2点执行）
0 2 * * * /path/to/batch_process.sh >> /var/log/whisper_batch.log 2>&1
```

## ⚠️ 注意事项

1. **大文件处理**: 对于很大的音频文件，处理时间会相应增加
2. **磁盘空间**: 确保有足够的磁盘空间存储转录结果
3. **系统资源**: 处理过程会占用较多 CPU 和内存资源
4. **模型选择**: 更大的模型准确度更高，但处理速度更慢

## 🐛 错误处理示例

### 处理失败的情况
```
❌ [3/8] 处理失败: corrupted_audio.mp3 - ffmpeg 音频转换失败
❌ [5/8] 处理失败: empty_file.wav - 文件名包含无效字符
```

### 日志输出
```
2024-01-15 14:25:30 [ERROR] - 文件处理失败: corrupted_audio.mp3 - ffmpeg 音频转换失败 (耗时: 2.34秒)
2024-01-15 14:26:15 [ERROR] - 文件处理失败: empty_file.wav - 文件名包含无效字符 (耗时: 0.12秒)
```

这些示例展示了工具的完整使用流程和各种输出格式，帮助用户更好地理解和使用这个批量音频转录工具。
