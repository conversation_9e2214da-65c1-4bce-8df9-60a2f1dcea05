[package]
name = "whisper_batch_processor"
version = "0.1.0"
edition = "2021"
authors = ["Whisper Batch Processor Team"]
description = "A high-performance batch audio transcription tool based on Whisper.cpp"
license = "MIT"
repository = "https://github.com/your-username/whisper_batch_processor"
keywords = ["whisper", "audio", "transcription", "batch", "speech-to-text"]
categories = ["multimedia::audio", "command-line-utilities"]
readme = "README.md"

[dependencies]
walkdir = "2"
dialoguer = "0.11"
indicatif = "0.18.0"
log = "0.4.27"
env_logger = "0.11.8"
chrono = { version = "0.4.41", features = ["serde"] }
clap = { version = "4.0", features = ["derive"] }

[dev-dependencies]
tempfile = "3.20.0"
