#!/bin/bash

echo "🧪 编码修复和检测优化测试"
echo "=========================="

# 清理旧的测试结果
echo "🧹 清理旧的测试结果..."
rm -rf script/*.srt
rm -rf script/reports/*

echo "🚀 启动新版本批量处理工具..."
echo "请选择一个包含少量音频文件的目录进行测试"
echo ""

# 运行新版本
./target/release/whisper_batch_processor

echo ""
echo "📊 测试完成后检查结果:"
echo "1. 检查SRT文件编码: file script/*.srt"
echo "2. 查看处理报告: cat script/reports/whisper_batch_report_*.txt"
echo "3. 验证中文显示: head -20 script/*.srt"
